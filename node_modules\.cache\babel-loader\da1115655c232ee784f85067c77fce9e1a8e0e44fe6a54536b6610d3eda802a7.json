{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport ListContext from '../List/ListContext';\nimport { getListItemSecondaryActionClassesUtilityClass } from './listItemSecondaryActionClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    disableGutters,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', disableGutters && 'disableGutters']\n  };\n  return composeClasses(slots, getListItemSecondaryActionClassesUtilityClass, classes);\n};\nconst ListItemSecondaryActionRoot = styled('div', {\n  name: 'MuiListItemSecondaryAction',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.disableGutters && styles.disableGutters];\n  }\n})(({\n  ownerState\n}) => _extends({\n  position: 'absolute',\n  right: 16,\n  top: '50%',\n  transform: 'translateY(-50%)'\n}, ownerState.disableGutters && {\n  right: 0\n}));\n\n/**\n * Must be used as the last child of ListItem to function properly.\n */\nconst ListItemSecondaryAction = /*#__PURE__*/React.forwardRef(function ListItemSecondaryAction(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemSecondaryAction'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const context = React.useContext(ListContext);\n  const ownerState = _extends({}, props, {\n    disableGutters: context.disableGutters\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ListItemSecondaryActionRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemSecondaryAction.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally an `IconButton` or selection control.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nListItemSecondaryAction.muiName = 'ListItemSecondaryAction';\nexport default ListItemSecondaryAction;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "ListContext", "getListItemSecondaryActionClassesUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "disableGutters", "classes", "slots", "root", "ListItemSecondaryActionRoot", "name", "slot", "overridesResolver", "props", "styles", "position", "right", "top", "transform", "ListItemSecondaryAction", "forwardRef", "inProps", "ref", "className", "other", "context", "useContext", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool", "mui<PERSON><PERSON>"], "sources": ["D:/Safety Tracking Web App/node_modules/@mui/material/ListItemSecondaryAction/ListItemSecondaryAction.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport ListContext from '../List/ListContext';\nimport { getListItemSecondaryActionClassesUtilityClass } from './listItemSecondaryActionClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    disableGutters,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', disableGutters && 'disableGutters']\n  };\n  return composeClasses(slots, getListItemSecondaryActionClassesUtilityClass, classes);\n};\nconst ListItemSecondaryActionRoot = styled('div', {\n  name: 'MuiListItemSecondaryAction',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.disableGutters && styles.disableGutters];\n  }\n})(({\n  ownerState\n}) => _extends({\n  position: 'absolute',\n  right: 16,\n  top: '50%',\n  transform: 'translateY(-50%)'\n}, ownerState.disableGutters && {\n  right: 0\n}));\n\n/**\n * Must be used as the last child of ListItem to function properly.\n */\nconst ListItemSecondaryAction = /*#__PURE__*/React.forwardRef(function ListItemSecondaryAction(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemSecondaryAction'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const context = React.useContext(ListContext);\n  const ownerState = _extends({}, props, {\n    disableGutters: context.disableGutters\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ListItemSecondaryActionRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemSecondaryAction.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally an `IconButton` or selection control.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nListItemSecondaryAction.muiName = 'ListItemSecondaryAction';\nexport default ListItemSecondaryAction;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,CAAC;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,6CAA6C,QAAQ,kCAAkC;AAChG,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,cAAc;IACdC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,cAAc,IAAI,gBAAgB;EACnD,CAAC;EACD,OAAOT,cAAc,CAACW,KAAK,EAAEP,6CAA6C,EAAEM,OAAO,CAAC;AACtF,CAAC;AACD,MAAMG,2BAA2B,GAAGZ,MAAM,CAAC,KAAK,EAAE;EAChDa,IAAI,EAAE,4BAA4B;EAClCC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEJ,UAAU,CAACC,cAAc,IAAIS,MAAM,CAACT,cAAc,CAAC;EAC1E;AACF,CAAC,CAAC,CAAC,CAAC;EACFD;AACF,CAAC,KAAKb,QAAQ,CAAC;EACbwB,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,EAAE;EACTC,GAAG,EAAE,KAAK;EACVC,SAAS,EAAE;AACb,CAAC,EAAEd,UAAU,CAACC,cAAc,IAAI;EAC9BW,KAAK,EAAE;AACT,CAAC,CAAC,CAAC;;AAEH;AACA;AACA;AACA,MAAMG,uBAAuB,GAAG,aAAa1B,KAAK,CAAC2B,UAAU,CAAC,SAASD,uBAAuBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3G,MAAMT,KAAK,GAAGf,eAAe,CAAC;IAC5Be,KAAK,EAAEQ,OAAO;IACdX,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFa;IACF,CAAC,GAAGV,KAAK;IACTW,KAAK,GAAGlC,6BAA6B,CAACuB,KAAK,EAAErB,SAAS,CAAC;EACzD,MAAMiC,OAAO,GAAGhC,KAAK,CAACiC,UAAU,CAAC3B,WAAW,CAAC;EAC7C,MAAMK,UAAU,GAAGb,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IACrCR,cAAc,EAAEoB,OAAO,CAACpB;EAC1B,CAAC,CAAC;EACF,MAAMC,OAAO,GAAGH,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACO,2BAA2B,EAAElB,QAAQ,CAAC;IAC7DgC,SAAS,EAAE5B,IAAI,CAACW,OAAO,CAACE,IAAI,EAAEe,SAAS,CAAC;IACxCnB,UAAU,EAAEA,UAAU;IACtBkB,GAAG,EAAEA;EACP,CAAC,EAAEE,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGV,uBAAuB,CAACW,SAAS,CAAC,yBAAyB;EACjG;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAErC,SAAS,CAACsC,IAAI;EACxB;AACF;AACA;EACE1B,OAAO,EAAEZ,SAAS,CAACuC,MAAM;EACzB;AACF;AACA;EACEV,SAAS,EAAE7B,SAAS,CAACwC,MAAM;EAC3B;AACF;AACA;EACEC,EAAE,EAAEzC,SAAS,CAAC0C,SAAS,CAAC,CAAC1C,SAAS,CAAC2C,OAAO,CAAC3C,SAAS,CAAC0C,SAAS,CAAC,CAAC1C,SAAS,CAAC4C,IAAI,EAAE5C,SAAS,CAACuC,MAAM,EAAEvC,SAAS,CAAC6C,IAAI,CAAC,CAAC,CAAC,EAAE7C,SAAS,CAAC4C,IAAI,EAAE5C,SAAS,CAACuC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACVd,uBAAuB,CAACqB,OAAO,GAAG,yBAAyB;AAC3D,eAAerB,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}