{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getNativeSelectUtilityClasses(slot) {\n  return generateUtilityClass('MuiNativeSelect', slot);\n}\nconst nativeSelectClasses = generateUtilityClasses('MuiNativeSelect', ['root', 'select', 'multiple', 'filled', 'outlined', 'standard', 'disabled', 'icon', 'iconOpen', 'iconFilled', 'iconOutlined', 'iconStandard', 'nativeInput', 'error']);\nexport default nativeSelectClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getNativeSelectUtilityClasses", "slot", "nativeSelectClasses"], "sources": ["D:/Safety Tracking Web App/node_modules/@mui/material/NativeSelect/nativeSelectClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getNativeSelectUtilityClasses(slot) {\n  return generateUtilityClass('MuiNativeSelect', slot);\n}\nconst nativeSelectClasses = generateUtilityClasses('MuiNativeSelect', ['root', 'select', 'multiple', 'filled', 'outlined', 'standard', 'disabled', 'icon', 'iconOpen', 'iconFilled', 'iconOutlined', 'iconStandard', 'nativeInput', 'error']);\nexport default nativeSelectClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,6BAA6BA,CAACC,IAAI,EAAE;EAClD,OAAOF,oBAAoB,CAAC,iBAAiB,EAAEE,IAAI,CAAC;AACtD;AACA,MAAMC,mBAAmB,GAAGJ,sBAAsB,CAAC,iBAAiB,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;AAC7O,eAAeI,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}