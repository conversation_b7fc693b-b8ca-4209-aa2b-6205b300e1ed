{"ast": null, "code": "function easeInOutSin(time) {\n  return (1 + Math.sin(Math.PI * time - Math.PI / 2)) / 2;\n}\nexport default function animate(property, element, to, options = {}, cb = () => {}) {\n  const {\n    ease = easeInOutSin,\n    duration = 300 // standard\n  } = options;\n  let start = null;\n  const from = element[property];\n  let cancelled = false;\n  const cancel = () => {\n    cancelled = true;\n  };\n  const step = timestamp => {\n    if (cancelled) {\n      cb(new Error('Animation cancelled'));\n      return;\n    }\n    if (start === null) {\n      start = timestamp;\n    }\n    const time = Math.min(1, (timestamp - start) / duration);\n    element[property] = ease(time) * (to - from) + from;\n    if (time >= 1) {\n      requestAnimationFrame(() => {\n        cb(null);\n      });\n      return;\n    }\n    requestAnimationFrame(step);\n  };\n  if (from === to) {\n    cb(new Error('Element already at target position'));\n    return cancel;\n  }\n  requestAnimationFrame(step);\n  return cancel;\n}", "map": {"version": 3, "names": ["easeInOutSin", "time", "Math", "sin", "PI", "animate", "property", "element", "to", "options", "cb", "ease", "duration", "start", "from", "cancelled", "cancel", "step", "timestamp", "Error", "min", "requestAnimationFrame"], "sources": ["D:/Safety Tracking Web App/node_modules/@mui/material/internal/animate.js"], "sourcesContent": ["function easeInOutSin(time) {\n  return (1 + Math.sin(Math.PI * time - Math.PI / 2)) / 2;\n}\nexport default function animate(property, element, to, options = {}, cb = () => {}) {\n  const {\n    ease = easeInOutSin,\n    duration = 300 // standard\n  } = options;\n  let start = null;\n  const from = element[property];\n  let cancelled = false;\n  const cancel = () => {\n    cancelled = true;\n  };\n  const step = timestamp => {\n    if (cancelled) {\n      cb(new Error('Animation cancelled'));\n      return;\n    }\n    if (start === null) {\n      start = timestamp;\n    }\n    const time = Math.min(1, (timestamp - start) / duration);\n    element[property] = ease(time) * (to - from) + from;\n    if (time >= 1) {\n      requestAnimationFrame(() => {\n        cb(null);\n      });\n      return;\n    }\n    requestAnimationFrame(step);\n  };\n  if (from === to) {\n    cb(new Error('Element already at target position'));\n    return cancel;\n  }\n  requestAnimationFrame(step);\n  return cancel;\n}"], "mappings": "AAAA,SAASA,YAAYA,CAACC,IAAI,EAAE;EAC1B,OAAO,CAAC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,EAAE,GAAGH,IAAI,GAAGC,IAAI,CAACE,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC;AACzD;AACA,eAAe,SAASC,OAAOA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,EAAE,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAEC,EAAE,GAAGA,CAAA,KAAM,CAAC,CAAC,EAAE;EAClF,MAAM;IACJC,IAAI,GAAGX,YAAY;IACnBY,QAAQ,GAAG,GAAG,CAAC;EACjB,CAAC,GAAGH,OAAO;EACX,IAAII,KAAK,GAAG,IAAI;EAChB,MAAMC,IAAI,GAAGP,OAAO,CAACD,QAAQ,CAAC;EAC9B,IAAIS,SAAS,GAAG,KAAK;EACrB,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACnBD,SAAS,GAAG,IAAI;EAClB,CAAC;EACD,MAAME,IAAI,GAAGC,SAAS,IAAI;IACxB,IAAIH,SAAS,EAAE;MACbL,EAAE,CAAC,IAAIS,KAAK,CAAC,qBAAqB,CAAC,CAAC;MACpC;IACF;IACA,IAAIN,KAAK,KAAK,IAAI,EAAE;MAClBA,KAAK,GAAGK,SAAS;IACnB;IACA,MAAMjB,IAAI,GAAGC,IAAI,CAACkB,GAAG,CAAC,CAAC,EAAE,CAACF,SAAS,GAAGL,KAAK,IAAID,QAAQ,CAAC;IACxDL,OAAO,CAACD,QAAQ,CAAC,GAAGK,IAAI,CAACV,IAAI,CAAC,IAAIO,EAAE,GAAGM,IAAI,CAAC,GAAGA,IAAI;IACnD,IAAIb,IAAI,IAAI,CAAC,EAAE;MACboB,qBAAqB,CAAC,MAAM;QAC1BX,EAAE,CAAC,IAAI,CAAC;MACV,CAAC,CAAC;MACF;IACF;IACAW,qBAAqB,CAACJ,IAAI,CAAC;EAC7B,CAAC;EACD,IAAIH,IAAI,KAAKN,EAAE,EAAE;IACfE,EAAE,CAAC,IAAIS,KAAK,CAAC,oCAAoC,CAAC,CAAC;IACnD,OAAOH,MAAM;EACf;EACAK,qBAAqB,CAACJ,IAAI,CAAC;EAC3B,OAAOD,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}