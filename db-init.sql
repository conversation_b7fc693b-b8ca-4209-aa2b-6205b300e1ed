-- Run this script to initialize your local database

CREATE TABLE IF NOT EXISTS equipment_categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT
);

CREATE TABLE IF NOT EXISTS equipment (
  id SERIAL PRIMARY KEY,
  unique_code VARCHAR(50) UNIQUE NOT NULL,
  category_id INTEGER REFERENCES equipment_categories(id),
  name VARCHAR(100) NOT NULL,
  acquisition_date DATE NOT NULL,
  condition VARCHAR(20) CHECK (condition IN ('new', 'used', 'repaired')),
  lifecycle_days INTEGER,
  last_inspection_date DATE,
  notes TEXT
);

-- Insert some initial data
INSERT INTO equipment_categories (name, description) VALUES 
('Legs', 'Support legs for stabilizing equipment'),
('Hooks', 'Cargo hooks for lifting operations'),
('Slings', 'Lifting slings for various cargo types');