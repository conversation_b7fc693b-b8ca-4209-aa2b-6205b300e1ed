import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { 
  AppBar, Toolbar, Typography, Button, Drawer, 
  List, ListItem, ListItemIcon, ListItemText, Box 
} from '@mui/material';
import DashboardIcon from '@mui/icons-material/Dashboard';
import InventoryIcon from '@mui/icons-material/Inventory';

function Navigation() {
  return (
    <>
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Cargo Equipment Manager
          </Typography>
          <Button color="inherit" component={RouterLink} to="/">Dashboard</Button>
          <Button color="inherit" component={RouterLink} to="/equipment">Equipment</Button>
        </Toolbar>
      </AppBar>
      
      <Drawer variant="permanent" sx={{ 
        width: 240,
        flexShrink: 0,
        [`& .MuiDrawer-paper`]: { width: 240, boxSizing: 'border-box' },
        display: { xs: 'none', sm: 'block' } 
      }}>
        <Toolbar />
        <Box sx={{ overflow: 'auto' }}>
          <List>
            <ListItem button component={RouterLink} to="/">
              <ListItemIcon><DashboardIcon /></ListItemIcon>
              <ListItemText primary="Dashboard" />
            </ListItem>
            <ListItem button component={RouterLink} to="/equipment">
              <ListItemIcon><InventoryIcon /></ListItemIcon>
              <ListItemText primary="Equipment" />
            </ListItem>
          </List>
        </Box>
      </Drawer>
    </>
  );
}

export default Navigation;