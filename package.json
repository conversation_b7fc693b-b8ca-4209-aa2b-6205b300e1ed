{"name": "cargo-equipment-management", "version": "0.1.0", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "server": "nodemon server/index.js", "dev": "concurrently \"npm run server\" \"npm run start\""}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.14.0", "@mui/material": "^5.14.0", "@mui/icons-material": "^5.14.0", "express": "^4.18.2", "pg": "^8.11.0", "jsonwebtoken": "^9.0.0", "bcrypt": "^5.1.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^2.0.22", "concurrently": "^8.2.0"}}