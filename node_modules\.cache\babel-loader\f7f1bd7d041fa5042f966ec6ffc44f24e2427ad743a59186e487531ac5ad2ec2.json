{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getIconButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiIconButton', slot);\n}\nconst iconButtonClasses = generateUtilityClasses('MuiIconButton', ['root', 'disabled', 'colorInherit', 'colorPrimary', 'colorSecondary', 'colorError', 'colorInfo', 'colorSuccess', 'colorWarning', 'edgeStart', 'edgeEnd', 'sizeSmall', 'sizeMedium', 'sizeLarge']);\nexport default iconButtonClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getIconButtonUtilityClass", "slot", "iconButtonClasses"], "sources": ["D:/Safety Tracking Web App/node_modules/@mui/material/IconButton/iconButtonClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getIconButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiIconButton', slot);\n}\nconst iconButtonClasses = generateUtilityClasses('MuiIconButton', ['root', 'disabled', 'colorInherit', 'colorPrimary', 'colorSecondary', 'colorError', 'colorInfo', 'colorSuccess', 'colorWarning', 'edgeStart', 'edgeEnd', 'sizeSmall', 'sizeMedium', 'sizeLarge']);\nexport default iconButtonClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAOF,oBAAoB,CAAC,eAAe,EAAEE,IAAI,CAAC;AACpD;AACA,MAAMC,iBAAiB,GAAGJ,sBAAsB,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;AACpQ,eAAeI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}