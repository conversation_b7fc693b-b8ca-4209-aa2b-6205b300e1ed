{"ast": null, "code": "import hoistNonReactStatics$1 from 'hoist-non-react-statics';\n\n// this file isolates this package that is not tree-shakeable\n// and if this module doesn't actually contain any logic of its own\n// then Rollup just use 'hoist-non-react-statics' directly in other chunks\n\nvar hoistNonReactStatics = function (targetComponent, sourceComponent) {\n  return hoistNonReactStatics$1(targetComponent, sourceComponent);\n};\nexport { hoistNonReactStatics as default };", "map": {"version": 3, "names": ["hoistNonReactStatics$1", "hoistNonReactStatics", "targetComponent", "sourceComponent", "default"], "sources": ["D:/Safety Tracking Web App/node_modules/@emotion/react/_isolated-hnrs/dist/emotion-react-_isolated-hnrs.browser.development.esm.js"], "sourcesContent": ["import hoistNonReactStatics$1 from 'hoist-non-react-statics';\n\n// this file isolates this package that is not tree-shakeable\n// and if this module doesn't actually contain any logic of its own\n// then Rollup just use 'hoist-non-react-statics' directly in other chunks\n\nvar hoistNonReactStatics = (function (targetComponent, sourceComponent) {\n  return hoistNonReactStatics$1(targetComponent, sourceComponent);\n});\n\nexport { hoistNonReactStatics as default };\n"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,yBAAyB;;AAE5D;AACA;AACA;;AAEA,IAAIC,oBAAoB,GAAI,SAAAA,CAAUC,eAAe,EAAEC,eAAe,EAAE;EACtE,OAAOH,sBAAsB,CAACE,eAAe,EAAEC,eAAe,CAAC;AACjE,CAAE;AAEF,SAASF,oBAAoB,IAAIG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}