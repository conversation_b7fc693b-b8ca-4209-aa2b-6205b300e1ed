{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar _span;\nconst _excluded = [\"children\", \"className\", \"component\", \"disabled\", \"error\", \"filled\", \"focused\", \"margin\", \"required\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport formControlState from '../FormControl/formControlState';\nimport useFormControl from '../FormControl/useFormControl';\nimport styled from '../styles/styled';\nimport capitalize from '../utils/capitalize';\nimport formHelperTextClasses, { getFormHelperTextUtilityClasses } from './formHelperTextClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    contained,\n    size,\n    disabled,\n    error,\n    filled,\n    focused,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', error && 'error', size && `size${capitalize(size)}`, contained && 'contained', focused && 'focused', filled && 'filled', required && 'required']\n  };\n  return composeClasses(slots, getFormHelperTextUtilityClasses, classes);\n};\nconst FormHelperTextRoot = styled('p', {\n  name: 'MuiFormHelperText',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.size && styles[`size${capitalize(ownerState.size)}`], ownerState.contained && styles.contained, ownerState.filled && styles.filled];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  color: (theme.vars || theme).palette.text.secondary\n}, theme.typography.caption, {\n  textAlign: 'left',\n  marginTop: 3,\n  marginRight: 0,\n  marginBottom: 0,\n  marginLeft: 0,\n  [`&.${formHelperTextClasses.disabled}`]: {\n    color: (theme.vars || theme).palette.text.disabled\n  },\n  [`&.${formHelperTextClasses.error}`]: {\n    color: (theme.vars || theme).palette.error.main\n  }\n}, ownerState.size === 'small' && {\n  marginTop: 4\n}, ownerState.contained && {\n  marginLeft: 14,\n  marginRight: 14\n}));\nconst FormHelperText = /*#__PURE__*/React.forwardRef(function FormHelperText(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFormHelperText'\n  });\n  const {\n      children,\n      className,\n      component = 'p'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl();\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['variant', 'size', 'disabled', 'error', 'filled', 'focused', 'required']\n  });\n  const ownerState = _extends({}, props, {\n    component,\n    contained: fcs.variant === 'filled' || fcs.variant === 'outlined',\n    variant: fcs.variant,\n    size: fcs.size,\n    disabled: fcs.disabled,\n    error: fcs.error,\n    filled: fcs.filled,\n    focused: fcs.focused,\n    required: fcs.required\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(FormHelperTextRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: children === ' ' ?\n    // notranslate needed while Google Translate will not fix zero-width space issue\n    _span || (_span = /*#__PURE__*/_jsx(\"span\", {\n      className: \"notranslate\",\n      children: \"\\u200B\"\n    })) : children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormHelperText.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   *\n   * If `' '` is provided, the component reserves one line height for displaying a future message.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the helper text should be displayed in a disabled state.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, helper text should be displayed in an error state.\n   */\n  error: PropTypes.bool,\n  /**\n   * If `true`, the helper text should use filled classes key.\n   */\n  filled: PropTypes.bool,\n  /**\n   * If `true`, the helper text should use focused classes key.\n   */\n  focused: PropTypes.bool,\n  /**\n   * If `dense`, will adjust vertical spacing. This is normally obtained via context from\n   * FormControl.\n   */\n  margin: PropTypes.oneOf(['dense']),\n  /**\n   * If `true`, the helper text should use required classes key.\n   */\n  required: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['filled', 'outlined', 'standard']), PropTypes.string])\n} : void 0;\nexport default FormHelperText;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_span", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "formControlState", "useFormControl", "styled", "capitalize", "formHelperTextClasses", "getFormHelperTextUtilityClasses", "useDefaultProps", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "contained", "size", "disabled", "error", "filled", "focused", "required", "slots", "root", "FormHelperTextRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "color", "vars", "palette", "text", "secondary", "typography", "caption", "textAlign", "marginTop", "marginRight", "marginBottom", "marginLeft", "main", "FormHelperText", "forwardRef", "inProps", "ref", "children", "className", "component", "other", "muiFormControl", "fcs", "states", "variant", "as", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "elementType", "bool", "margin", "oneOf", "sx", "oneOfType", "arrayOf", "func"], "sources": ["D:/Safety Tracking Web App/node_modules/@mui/material/FormHelperText/FormHelperText.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar _span;\nconst _excluded = [\"children\", \"className\", \"component\", \"disabled\", \"error\", \"filled\", \"focused\", \"margin\", \"required\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport formControlState from '../FormControl/formControlState';\nimport useFormControl from '../FormControl/useFormControl';\nimport styled from '../styles/styled';\nimport capitalize from '../utils/capitalize';\nimport formHelperTextClasses, { getFormHelperTextUtilityClasses } from './formHelperTextClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    contained,\n    size,\n    disabled,\n    error,\n    filled,\n    focused,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', error && 'error', size && `size${capitalize(size)}`, contained && 'contained', focused && 'focused', filled && 'filled', required && 'required']\n  };\n  return composeClasses(slots, getFormHelperTextUtilityClasses, classes);\n};\nconst FormHelperTextRoot = styled('p', {\n  name: 'MuiFormHelperText',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.size && styles[`size${capitalize(ownerState.size)}`], ownerState.contained && styles.contained, ownerState.filled && styles.filled];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  color: (theme.vars || theme).palette.text.secondary\n}, theme.typography.caption, {\n  textAlign: 'left',\n  marginTop: 3,\n  marginRight: 0,\n  marginBottom: 0,\n  marginLeft: 0,\n  [`&.${formHelperTextClasses.disabled}`]: {\n    color: (theme.vars || theme).palette.text.disabled\n  },\n  [`&.${formHelperTextClasses.error}`]: {\n    color: (theme.vars || theme).palette.error.main\n  }\n}, ownerState.size === 'small' && {\n  marginTop: 4\n}, ownerState.contained && {\n  marginLeft: 14,\n  marginRight: 14\n}));\nconst FormHelperText = /*#__PURE__*/React.forwardRef(function FormHelperText(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFormHelperText'\n  });\n  const {\n      children,\n      className,\n      component = 'p'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl();\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['variant', 'size', 'disabled', 'error', 'filled', 'focused', 'required']\n  });\n  const ownerState = _extends({}, props, {\n    component,\n    contained: fcs.variant === 'filled' || fcs.variant === 'outlined',\n    variant: fcs.variant,\n    size: fcs.size,\n    disabled: fcs.disabled,\n    error: fcs.error,\n    filled: fcs.filled,\n    focused: fcs.focused,\n    required: fcs.required\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(FormHelperTextRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: children === ' ' ? // notranslate needed while Google Translate will not fix zero-width space issue\n    _span || (_span = /*#__PURE__*/_jsx(\"span\", {\n      className: \"notranslate\",\n      children: \"\\u200B\"\n    })) : children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormHelperText.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   *\n   * If `' '` is provided, the component reserves one line height for displaying a future message.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the helper text should be displayed in a disabled state.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, helper text should be displayed in an error state.\n   */\n  error: PropTypes.bool,\n  /**\n   * If `true`, the helper text should use filled classes key.\n   */\n  filled: PropTypes.bool,\n  /**\n   * If `true`, the helper text should use focused classes key.\n   */\n  focused: PropTypes.bool,\n  /**\n   * If `dense`, will adjust vertical spacing. This is normally obtained via context from\n   * FormControl.\n   */\n  margin: PropTypes.oneOf(['dense']),\n  /**\n   * If `true`, the helper text should use required classes key.\n   */\n  required: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['filled', 'outlined', 'standard']), PropTypes.string])\n} : void 0;\nexport default FormHelperText;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,KAAK;AACT,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;AACnI,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,qBAAqB,IAAIC,+BAA+B,QAAQ,yBAAyB;AAChG,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,SAAS;IACTC,IAAI;IACJC,QAAQ;IACRC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC;EACF,CAAC,GAAGR,UAAU;EACd,MAAMS,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEN,QAAQ,IAAI,UAAU,EAAEC,KAAK,IAAI,OAAO,EAAEF,IAAI,IAAI,OAAOV,UAAU,CAACU,IAAI,CAAC,EAAE,EAAED,SAAS,IAAI,WAAW,EAAEK,OAAO,IAAI,SAAS,EAAED,MAAM,IAAI,QAAQ,EAAEE,QAAQ,IAAI,UAAU;EACxL,CAAC;EACD,OAAOnB,cAAc,CAACoB,KAAK,EAAEd,+BAA+B,EAAEM,OAAO,CAAC;AACxE,CAAC;AACD,MAAMU,kBAAkB,GAAGnB,MAAM,CAAC,GAAG,EAAE;EACrCoB,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJhB;IACF,CAAC,GAAGe,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEV,UAAU,CAACG,IAAI,IAAIa,MAAM,CAAC,OAAOvB,UAAU,CAACO,UAAU,CAACG,IAAI,CAAC,EAAE,CAAC,EAAEH,UAAU,CAACE,SAAS,IAAIc,MAAM,CAACd,SAAS,EAAEF,UAAU,CAACM,MAAM,IAAIU,MAAM,CAACV,MAAM,CAAC;EACrK;AACF,CAAC,CAAC,CAAC,CAAC;EACFW,KAAK;EACLjB;AACF,CAAC,KAAKjB,QAAQ,CAAC;EACbmC,KAAK,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEG,OAAO,CAACC,IAAI,CAACC;AAC5C,CAAC,EAAEL,KAAK,CAACM,UAAU,CAACC,OAAO,EAAE;EAC3BC,SAAS,EAAE,MAAM;EACjBC,SAAS,EAAE,CAAC;EACZC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,CAAC;EACfC,UAAU,EAAE,CAAC;EACb,CAAC,KAAKnC,qBAAqB,CAACU,QAAQ,EAAE,GAAG;IACvCc,KAAK,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEG,OAAO,CAACC,IAAI,CAACjB;EAC5C,CAAC;EACD,CAAC,KAAKV,qBAAqB,CAACW,KAAK,EAAE,GAAG;IACpCa,KAAK,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEG,OAAO,CAACf,KAAK,CAACyB;EAC7C;AACF,CAAC,EAAE9B,UAAU,CAACG,IAAI,KAAK,OAAO,IAAI;EAChCuB,SAAS,EAAE;AACb,CAAC,EAAE1B,UAAU,CAACE,SAAS,IAAI;EACzB2B,UAAU,EAAE,EAAE;EACdF,WAAW,EAAE;AACf,CAAC,CAAC,CAAC;AACH,MAAMI,cAAc,GAAG,aAAa7C,KAAK,CAAC8C,UAAU,CAAC,SAASD,cAAcA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzF,MAAMnB,KAAK,GAAGnB,eAAe,CAAC;IAC5BmB,KAAK,EAAEkB,OAAO;IACdrB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFuB,QAAQ;MACRC,SAAS;MACTC,SAAS,GAAG;IACd,CAAC,GAAGtB,KAAK;IACTuB,KAAK,GAAGxD,6BAA6B,CAACiC,KAAK,EAAE9B,SAAS,CAAC;EACzD,MAAMsD,cAAc,GAAGhD,cAAc,CAAC,CAAC;EACvC,MAAMiD,GAAG,GAAGlD,gBAAgB,CAAC;IAC3ByB,KAAK;IACLwB,cAAc;IACdE,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU;EAClF,CAAC,CAAC;EACF,MAAMzC,UAAU,GAAGjB,QAAQ,CAAC,CAAC,CAAC,EAAEgC,KAAK,EAAE;IACrCsB,SAAS;IACTnC,SAAS,EAAEsC,GAAG,CAACE,OAAO,KAAK,QAAQ,IAAIF,GAAG,CAACE,OAAO,KAAK,UAAU;IACjEA,OAAO,EAAEF,GAAG,CAACE,OAAO;IACpBvC,IAAI,EAAEqC,GAAG,CAACrC,IAAI;IACdC,QAAQ,EAAEoC,GAAG,CAACpC,QAAQ;IACtBC,KAAK,EAAEmC,GAAG,CAACnC,KAAK;IAChBC,MAAM,EAAEkC,GAAG,CAAClC,MAAM;IAClBC,OAAO,EAAEiC,GAAG,CAACjC,OAAO;IACpBC,QAAQ,EAAEgC,GAAG,CAAChC;EAChB,CAAC,CAAC;EACF,MAAMP,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACa,kBAAkB,EAAE5B,QAAQ,CAAC;IACpD4D,EAAE,EAAEN,SAAS;IACbrC,UAAU,EAAEA,UAAU;IACtBoC,SAAS,EAAEhD,IAAI,CAACa,OAAO,CAACS,IAAI,EAAE0B,SAAS,CAAC;IACxCF,GAAG,EAAEA;EACP,CAAC,EAAEI,KAAK,EAAE;IACRH,QAAQ,EAAEA,QAAQ,KAAK,GAAG;IAAG;IAC7BnD,KAAK,KAAKA,KAAK,GAAG,aAAac,IAAI,CAAC,MAAM,EAAE;MAC1CsC,SAAS,EAAE,aAAa;MACxBD,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC,GAAGA;EACR,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGf,cAAc,CAACgB,SAAS,CAAC,yBAAyB;EACxF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;EACEZ,QAAQ,EAAEhD,SAAS,CAAC6D,IAAI;EACxB;AACF;AACA;EACE/C,OAAO,EAAEd,SAAS,CAAC8D,MAAM;EACzB;AACF;AACA;EACEb,SAAS,EAAEjD,SAAS,CAAC+D,MAAM;EAC3B;AACF;AACA;AACA;EACEb,SAAS,EAAElD,SAAS,CAACgE,WAAW;EAChC;AACF;AACA;EACE/C,QAAQ,EAAEjB,SAAS,CAACiE,IAAI;EACxB;AACF;AACA;EACE/C,KAAK,EAAElB,SAAS,CAACiE,IAAI;EACrB;AACF;AACA;EACE9C,MAAM,EAAEnB,SAAS,CAACiE,IAAI;EACtB;AACF;AACA;EACE7C,OAAO,EAAEpB,SAAS,CAACiE,IAAI;EACvB;AACF;AACA;AACA;EACEC,MAAM,EAAElE,SAAS,CAACmE,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;EAClC;AACF;AACA;EACE9C,QAAQ,EAAErB,SAAS,CAACiE,IAAI;EACxB;AACF;AACA;EACEG,EAAE,EAAEpE,SAAS,CAACqE,SAAS,CAAC,CAACrE,SAAS,CAACsE,OAAO,CAACtE,SAAS,CAACqE,SAAS,CAAC,CAACrE,SAAS,CAACuE,IAAI,EAAEvE,SAAS,CAAC8D,MAAM,EAAE9D,SAAS,CAACiE,IAAI,CAAC,CAAC,CAAC,EAAEjE,SAAS,CAACuE,IAAI,EAAEvE,SAAS,CAAC8D,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACEP,OAAO,EAAEvD,SAAS,CAAC,sCAAsCqE,SAAS,CAAC,CAACrE,SAAS,CAACmE,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAEnE,SAAS,CAAC+D,MAAM,CAAC;AAC5I,CAAC,GAAG,KAAK,CAAC;AACV,eAAenB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}