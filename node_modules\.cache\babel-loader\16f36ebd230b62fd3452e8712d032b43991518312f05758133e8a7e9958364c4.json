{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getStepContentUtilityClass(slot) {\n  return generateUtilityClass('MuiStepContent', slot);\n}\nconst stepContentClasses = generateUtilityClasses('MuiStepContent', ['root', 'last', 'transition']);\nexport default stepContentClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getStepContentUtilityClass", "slot", "stepContentClasses"], "sources": ["D:/Safety Tracking Web App/node_modules/@mui/material/StepContent/stepContentClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getStepContentUtilityClass(slot) {\n  return generateUtilityClass('MuiStepContent', slot);\n}\nconst stepContentClasses = generateUtilityClasses('MuiStepContent', ['root', 'last', 'transition']);\nexport default stepContentClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOF,oBAAoB,CAAC,gBAAgB,EAAEE,IAAI,CAAC;AACrD;AACA,MAAMC,kBAAkB,GAAGJ,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;AACnG,eAAeI,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}