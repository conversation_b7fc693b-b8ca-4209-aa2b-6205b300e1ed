{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"arrow\", \"children\", \"classes\", \"components\", \"componentsProps\", \"describeChild\", \"disableFocusListener\", \"disableHoverListener\", \"disableInteractive\", \"disableTouchListener\", \"enterDelay\", \"enterNextDelay\", \"enterTouchDelay\", \"followCursor\", \"id\", \"leaveDelay\", \"leaveTouchDelay\", \"onClose\", \"onOpen\", \"open\", \"placement\", \"PopperComponent\", \"PopperProps\", \"slotProps\", \"slots\", \"title\", \"TransitionComponent\", \"TransitionProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport useTimeout, { Timeout } from '@mui/utils/useTimeout';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport appendOwnerState from '@mui/utils/appendOwnerState';\nimport getReactElementRef from '@mui/utils/getReactElementRef';\nimport { styled, useTheme } from '../styles';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport capitalize from '../utils/capitalize';\nimport Grow from '../Grow';\nimport Popper from '../Popper';\nimport useEventCallback from '../utils/useEventCallback';\nimport useForkRef from '../utils/useForkRef';\nimport useId from '../utils/useId';\nimport useIsFocusVisible from '../utils/useIsFocusVisible';\nimport useControlled from '../utils/useControlled';\nimport tooltipClasses, { getTooltipUtilityClass } from './tooltipClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction round(value) {\n  return Math.round(value * 1e5) / 1e5;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableInteractive,\n    arrow,\n    touch,\n    placement\n  } = ownerState;\n  const slots = {\n    popper: ['popper', !disableInteractive && 'popperInteractive', arrow && 'popperArrow'],\n    tooltip: ['tooltip', arrow && 'tooltipArrow', touch && 'touch', `tooltipPlacement${capitalize(placement.split('-')[0])}`],\n    arrow: ['arrow']\n  };\n  return composeClasses(slots, getTooltipUtilityClass, classes);\n};\nconst TooltipPopper = styled(Popper, {\n  name: 'MuiTooltip',\n  slot: 'Popper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.popper, !ownerState.disableInteractive && styles.popperInteractive, ownerState.arrow && styles.popperArrow, !ownerState.open && styles.popperClose];\n  }\n})(({\n  theme,\n  ownerState,\n  open\n}) => _extends({\n  zIndex: (theme.vars || theme).zIndex.tooltip,\n  pointerEvents: 'none'\n}, !ownerState.disableInteractive && {\n  pointerEvents: 'auto'\n}, !open && {\n  pointerEvents: 'none'\n}, ownerState.arrow && {\n  [`&[data-popper-placement*=\"bottom\"] .${tooltipClasses.arrow}`]: {\n    top: 0,\n    marginTop: '-0.71em',\n    '&::before': {\n      transformOrigin: '0 100%'\n    }\n  },\n  [`&[data-popper-placement*=\"top\"] .${tooltipClasses.arrow}`]: {\n    bottom: 0,\n    marginBottom: '-0.71em',\n    '&::before': {\n      transformOrigin: '100% 0'\n    }\n  },\n  [`&[data-popper-placement*=\"right\"] .${tooltipClasses.arrow}`]: _extends({}, !ownerState.isRtl ? {\n    left: 0,\n    marginLeft: '-0.71em'\n  } : {\n    right: 0,\n    marginRight: '-0.71em'\n  }, {\n    height: '1em',\n    width: '0.71em',\n    '&::before': {\n      transformOrigin: '100% 100%'\n    }\n  }),\n  [`&[data-popper-placement*=\"left\"] .${tooltipClasses.arrow}`]: _extends({}, !ownerState.isRtl ? {\n    right: 0,\n    marginRight: '-0.71em'\n  } : {\n    left: 0,\n    marginLeft: '-0.71em'\n  }, {\n    height: '1em',\n    width: '0.71em',\n    '&::before': {\n      transformOrigin: '0 0'\n    }\n  })\n}));\nconst TooltipTooltip = styled('div', {\n  name: 'MuiTooltip',\n  slot: 'Tooltip',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.tooltip, ownerState.touch && styles.touch, ownerState.arrow && styles.tooltipArrow, styles[`tooltipPlacement${capitalize(ownerState.placement.split('-')[0])}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  backgroundColor: theme.vars ? theme.vars.palette.Tooltip.bg : alpha(theme.palette.grey[700], 0.92),\n  borderRadius: (theme.vars || theme).shape.borderRadius,\n  color: (theme.vars || theme).palette.common.white,\n  fontFamily: theme.typography.fontFamily,\n  padding: '4px 8px',\n  fontSize: theme.typography.pxToRem(11),\n  maxWidth: 300,\n  margin: 2,\n  wordWrap: 'break-word',\n  fontWeight: theme.typography.fontWeightMedium\n}, ownerState.arrow && {\n  position: 'relative',\n  margin: 0\n}, ownerState.touch && {\n  padding: '8px 16px',\n  fontSize: theme.typography.pxToRem(14),\n  lineHeight: `${round(16 / 14)}em`,\n  fontWeight: theme.typography.fontWeightRegular\n}, {\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"left\"] &`]: _extends({\n    transformOrigin: 'right center'\n  }, !ownerState.isRtl ? _extends({\n    marginRight: '14px'\n  }, ownerState.touch && {\n    marginRight: '24px'\n  }) : _extends({\n    marginLeft: '14px'\n  }, ownerState.touch && {\n    marginLeft: '24px'\n  })),\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"right\"] &`]: _extends({\n    transformOrigin: 'left center'\n  }, !ownerState.isRtl ? _extends({\n    marginLeft: '14px'\n  }, ownerState.touch && {\n    marginLeft: '24px'\n  }) : _extends({\n    marginRight: '14px'\n  }, ownerState.touch && {\n    marginRight: '24px'\n  })),\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"top\"] &`]: _extends({\n    transformOrigin: 'center bottom',\n    marginBottom: '14px'\n  }, ownerState.touch && {\n    marginBottom: '24px'\n  }),\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"bottom\"] &`]: _extends({\n    transformOrigin: 'center top',\n    marginTop: '14px'\n  }, ownerState.touch && {\n    marginTop: '24px'\n  })\n}));\nconst TooltipArrow = styled('span', {\n  name: 'MuiTooltip',\n  slot: 'Arrow',\n  overridesResolver: (props, styles) => styles.arrow\n})(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  position: 'absolute',\n  width: '1em',\n  height: '0.71em' /* = width / sqrt(2) = (length of the hypotenuse) */,\n  boxSizing: 'border-box',\n  color: theme.vars ? theme.vars.palette.Tooltip.bg : alpha(theme.palette.grey[700], 0.9),\n  '&::before': {\n    content: '\"\"',\n    margin: 'auto',\n    display: 'block',\n    width: '100%',\n    height: '100%',\n    backgroundColor: 'currentColor',\n    transform: 'rotate(45deg)'\n  }\n}));\nlet hystersisOpen = false;\nconst hystersisTimer = new Timeout();\nlet cursorPosition = {\n  x: 0,\n  y: 0\n};\nexport function testReset() {\n  hystersisOpen = false;\n  hystersisTimer.clear();\n}\nfunction composeEventHandler(handler, eventHandler) {\n  return (event, ...params) => {\n    if (eventHandler) {\n      eventHandler(event, ...params);\n    }\n    handler(event, ...params);\n  };\n}\n\n// TODO v6: Remove PopperComponent, PopperProps, TransitionComponent and TransitionProps.\nconst Tooltip = /*#__PURE__*/React.forwardRef(function Tooltip(inProps, ref) {\n  var _ref, _slots$popper, _ref2, _ref3, _slots$transition, _ref4, _slots$tooltip, _ref5, _slots$arrow, _slotProps$popper, _ref6, _slotProps$popper2, _slotProps$transition, _slotProps$tooltip, _ref7, _slotProps$tooltip2, _slotProps$arrow, _ref8, _slotProps$arrow2;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTooltip'\n  });\n  const {\n      arrow = false,\n      children: childrenProp,\n      components = {},\n      componentsProps = {},\n      describeChild = false,\n      disableFocusListener = false,\n      disableHoverListener = false,\n      disableInteractive: disableInteractiveProp = false,\n      disableTouchListener = false,\n      enterDelay = 100,\n      enterNextDelay = 0,\n      enterTouchDelay = 700,\n      followCursor = false,\n      id: idProp,\n      leaveDelay = 0,\n      leaveTouchDelay = 1500,\n      onClose,\n      onOpen,\n      open: openProp,\n      placement = 'bottom',\n      PopperComponent: PopperComponentProp,\n      PopperProps = {},\n      slotProps = {},\n      slots = {},\n      title,\n      TransitionComponent: TransitionComponentProp = Grow,\n      TransitionProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  // to prevent runtime errors, developers will need to provide a child as a React element anyway.\n  const children = /*#__PURE__*/React.isValidElement(childrenProp) ? childrenProp : /*#__PURE__*/_jsx(\"span\", {\n    children: childrenProp\n  });\n  const theme = useTheme();\n  const isRtl = useRtl();\n  const [childNode, setChildNode] = React.useState();\n  const [arrowRef, setArrowRef] = React.useState(null);\n  const ignoreNonTouchEvents = React.useRef(false);\n  const disableInteractive = disableInteractiveProp || followCursor;\n  const closeTimer = useTimeout();\n  const enterTimer = useTimeout();\n  const leaveTimer = useTimeout();\n  const touchTimer = useTimeout();\n  const [openState, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: 'Tooltip',\n    state: 'open'\n  });\n  let open = openState;\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const {\n      current: isControlled\n    } = React.useRef(openProp !== undefined);\n\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (childNode && childNode.disabled && !isControlled && title !== '' && childNode.tagName.toLowerCase() === 'button') {\n        console.error(['MUI: You are providing a disabled `button` child to the Tooltip component.', 'A disabled element does not fire events.', \"Tooltip needs to listen to the child element's events to display the title.\", '', 'Add a simple wrapper element, such as a `span`.'].join('\\n'));\n      }\n    }, [title, childNode, isControlled]);\n  }\n  const id = useId(idProp);\n  const prevUserSelect = React.useRef();\n  const stopTouchInteraction = useEventCallback(() => {\n    if (prevUserSelect.current !== undefined) {\n      document.body.style.WebkitUserSelect = prevUserSelect.current;\n      prevUserSelect.current = undefined;\n    }\n    touchTimer.clear();\n  });\n  React.useEffect(() => stopTouchInteraction, [stopTouchInteraction]);\n  const handleOpen = event => {\n    hystersisTimer.clear();\n    hystersisOpen = true;\n\n    // The mouseover event will trigger for every nested element in the tooltip.\n    // We can skip rerendering when the tooltip is already open.\n    // We are using the mouseover event instead of the mouseenter event to fix a hide/show issue.\n    setOpenState(true);\n    if (onOpen && !open) {\n      onOpen(event);\n    }\n  };\n  const handleClose = useEventCallback(\n  /**\n   * @param {React.SyntheticEvent | Event} event\n   */\n  event => {\n    hystersisTimer.start(800 + leaveDelay, () => {\n      hystersisOpen = false;\n    });\n    setOpenState(false);\n    if (onClose && open) {\n      onClose(event);\n    }\n    closeTimer.start(theme.transitions.duration.shortest, () => {\n      ignoreNonTouchEvents.current = false;\n    });\n  });\n  const handleMouseOver = event => {\n    if (ignoreNonTouchEvents.current && event.type !== 'touchstart') {\n      return;\n    }\n\n    // Remove the title ahead of time.\n    // We don't want to wait for the next render commit.\n    // We would risk displaying two tooltips at the same time (native + this one).\n    if (childNode) {\n      childNode.removeAttribute('title');\n    }\n    enterTimer.clear();\n    leaveTimer.clear();\n    if (enterDelay || hystersisOpen && enterNextDelay) {\n      enterTimer.start(hystersisOpen ? enterNextDelay : enterDelay, () => {\n        handleOpen(event);\n      });\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleMouseLeave = event => {\n    enterTimer.clear();\n    leaveTimer.start(leaveDelay, () => {\n      handleClose(event);\n    });\n  };\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  // We don't necessarily care about the focusVisible state (which is safe to access via ref anyway).\n  // We just need to re-render the Tooltip if the focus-visible state changes.\n  const [, setChildIsFocusVisible] = React.useState(false);\n  const handleBlur = event => {\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setChildIsFocusVisible(false);\n      handleMouseLeave(event);\n    }\n  };\n  const handleFocus = event => {\n    // Workaround for https://github.com/facebook/react/issues/7769\n    // The autoFocus of React might trigger the event before the componentDidMount.\n    // We need to account for this eventuality.\n    if (!childNode) {\n      setChildNode(event.currentTarget);\n    }\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setChildIsFocusVisible(true);\n      handleMouseOver(event);\n    }\n  };\n  const detectTouchStart = event => {\n    ignoreNonTouchEvents.current = true;\n    const childrenProps = children.props;\n    if (childrenProps.onTouchStart) {\n      childrenProps.onTouchStart(event);\n    }\n  };\n  const handleTouchStart = event => {\n    detectTouchStart(event);\n    leaveTimer.clear();\n    closeTimer.clear();\n    stopTouchInteraction();\n    prevUserSelect.current = document.body.style.WebkitUserSelect;\n    // Prevent iOS text selection on long-tap.\n    document.body.style.WebkitUserSelect = 'none';\n    touchTimer.start(enterTouchDelay, () => {\n      document.body.style.WebkitUserSelect = prevUserSelect.current;\n      handleMouseOver(event);\n    });\n  };\n  const handleTouchEnd = event => {\n    if (children.props.onTouchEnd) {\n      children.props.onTouchEnd(event);\n    }\n    stopTouchInteraction();\n    leaveTimer.start(leaveTouchDelay, () => {\n      handleClose(event);\n    });\n  };\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n\n    /**\n     * @param {KeyboardEvent} nativeEvent\n     */\n    function handleKeyDown(nativeEvent) {\n      // IE11, Edge (prior to using Bink?) use 'Esc'\n      if (nativeEvent.key === 'Escape' || nativeEvent.key === 'Esc') {\n        handleClose(nativeEvent);\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [handleClose, open]);\n  const handleRef = useForkRef(getReactElementRef(children), focusVisibleRef, setChildNode, ref);\n\n  // There is no point in displaying an empty tooltip.\n  // So we exclude all falsy values, except 0, which is valid.\n  if (!title && title !== 0) {\n    open = false;\n  }\n  const popperRef = React.useRef();\n  const handleMouseMove = event => {\n    const childrenProps = children.props;\n    if (childrenProps.onMouseMove) {\n      childrenProps.onMouseMove(event);\n    }\n    cursorPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    if (popperRef.current) {\n      popperRef.current.update();\n    }\n  };\n  const nameOrDescProps = {};\n  const titleIsString = typeof title === 'string';\n  if (describeChild) {\n    nameOrDescProps.title = !open && titleIsString && !disableHoverListener ? title : null;\n    nameOrDescProps['aria-describedby'] = open ? id : null;\n  } else {\n    nameOrDescProps['aria-label'] = titleIsString ? title : null;\n    nameOrDescProps['aria-labelledby'] = open && !titleIsString ? id : null;\n  }\n  const childrenProps = _extends({}, nameOrDescProps, other, children.props, {\n    className: clsx(other.className, children.props.className),\n    onTouchStart: detectTouchStart,\n    ref: handleRef\n  }, followCursor ? {\n    onMouseMove: handleMouseMove\n  } : {});\n  if (process.env.NODE_ENV !== 'production') {\n    childrenProps['data-mui-internal-clone-element'] = true;\n\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (childNode && !childNode.getAttribute('data-mui-internal-clone-element')) {\n        console.error(['MUI: The `children` component of the Tooltip is not forwarding its props correctly.', 'Please make sure that props are spread on the same element that the ref is applied to.'].join('\\n'));\n      }\n    }, [childNode]);\n  }\n  const interactiveWrapperListeners = {};\n  if (!disableTouchListener) {\n    childrenProps.onTouchStart = handleTouchStart;\n    childrenProps.onTouchEnd = handleTouchEnd;\n  }\n  if (!disableHoverListener) {\n    childrenProps.onMouseOver = composeEventHandler(handleMouseOver, childrenProps.onMouseOver);\n    childrenProps.onMouseLeave = composeEventHandler(handleMouseLeave, childrenProps.onMouseLeave);\n    if (!disableInteractive) {\n      interactiveWrapperListeners.onMouseOver = handleMouseOver;\n      interactiveWrapperListeners.onMouseLeave = handleMouseLeave;\n    }\n  }\n  if (!disableFocusListener) {\n    childrenProps.onFocus = composeEventHandler(handleFocus, childrenProps.onFocus);\n    childrenProps.onBlur = composeEventHandler(handleBlur, childrenProps.onBlur);\n    if (!disableInteractive) {\n      interactiveWrapperListeners.onFocus = handleFocus;\n      interactiveWrapperListeners.onBlur = handleBlur;\n    }\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (children.props.title) {\n      console.error(['MUI: You have provided a `title` prop to the child of <Tooltip />.', `Remove this title prop \\`${children.props.title}\\` or the Tooltip component.`].join('\\n'));\n    }\n  }\n  const popperOptions = React.useMemo(() => {\n    var _PopperProps$popperOp;\n    let tooltipModifiers = [{\n      name: 'arrow',\n      enabled: Boolean(arrowRef),\n      options: {\n        element: arrowRef,\n        padding: 4\n      }\n    }];\n    if ((_PopperProps$popperOp = PopperProps.popperOptions) != null && _PopperProps$popperOp.modifiers) {\n      tooltipModifiers = tooltipModifiers.concat(PopperProps.popperOptions.modifiers);\n    }\n    return _extends({}, PopperProps.popperOptions, {\n      modifiers: tooltipModifiers\n    });\n  }, [arrowRef, PopperProps]);\n  const ownerState = _extends({}, props, {\n    isRtl,\n    arrow,\n    disableInteractive,\n    placement,\n    PopperComponentProp,\n    touch: ignoreNonTouchEvents.current\n  });\n  const classes = useUtilityClasses(ownerState);\n  const PopperComponent = (_ref = (_slots$popper = slots.popper) != null ? _slots$popper : components.Popper) != null ? _ref : TooltipPopper;\n  const TransitionComponent = (_ref2 = (_ref3 = (_slots$transition = slots.transition) != null ? _slots$transition : components.Transition) != null ? _ref3 : TransitionComponentProp) != null ? _ref2 : Grow;\n  const TooltipComponent = (_ref4 = (_slots$tooltip = slots.tooltip) != null ? _slots$tooltip : components.Tooltip) != null ? _ref4 : TooltipTooltip;\n  const ArrowComponent = (_ref5 = (_slots$arrow = slots.arrow) != null ? _slots$arrow : components.Arrow) != null ? _ref5 : TooltipArrow;\n  const popperProps = appendOwnerState(PopperComponent, _extends({}, PopperProps, (_slotProps$popper = slotProps.popper) != null ? _slotProps$popper : componentsProps.popper, {\n    className: clsx(classes.popper, PopperProps == null ? void 0 : PopperProps.className, (_ref6 = (_slotProps$popper2 = slotProps.popper) != null ? _slotProps$popper2 : componentsProps.popper) == null ? void 0 : _ref6.className)\n  }), ownerState);\n  const transitionProps = appendOwnerState(TransitionComponent, _extends({}, TransitionProps, (_slotProps$transition = slotProps.transition) != null ? _slotProps$transition : componentsProps.transition), ownerState);\n  const tooltipProps = appendOwnerState(TooltipComponent, _extends({}, (_slotProps$tooltip = slotProps.tooltip) != null ? _slotProps$tooltip : componentsProps.tooltip, {\n    className: clsx(classes.tooltip, (_ref7 = (_slotProps$tooltip2 = slotProps.tooltip) != null ? _slotProps$tooltip2 : componentsProps.tooltip) == null ? void 0 : _ref7.className)\n  }), ownerState);\n  const tooltipArrowProps = appendOwnerState(ArrowComponent, _extends({}, (_slotProps$arrow = slotProps.arrow) != null ? _slotProps$arrow : componentsProps.arrow, {\n    className: clsx(classes.arrow, (_ref8 = (_slotProps$arrow2 = slotProps.arrow) != null ? _slotProps$arrow2 : componentsProps.arrow) == null ? void 0 : _ref8.className)\n  }), ownerState);\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/React.cloneElement(children, childrenProps), /*#__PURE__*/_jsx(PopperComponent, _extends({\n      as: PopperComponentProp != null ? PopperComponentProp : Popper,\n      placement: placement,\n      anchorEl: followCursor ? {\n        getBoundingClientRect: () => ({\n          top: cursorPosition.y,\n          left: cursorPosition.x,\n          right: cursorPosition.x,\n          bottom: cursorPosition.y,\n          width: 0,\n          height: 0\n        })\n      } : childNode,\n      popperRef: popperRef,\n      open: childNode ? open : false,\n      id: id,\n      transition: true\n    }, interactiveWrapperListeners, popperProps, {\n      popperOptions: popperOptions,\n      children: ({\n        TransitionProps: TransitionPropsInner\n      }) => /*#__PURE__*/_jsx(TransitionComponent, _extends({\n        timeout: theme.transitions.duration.shorter\n      }, TransitionPropsInner, transitionProps, {\n        children: /*#__PURE__*/_jsxs(TooltipComponent, _extends({}, tooltipProps, {\n          children: [title, arrow ? /*#__PURE__*/_jsx(ArrowComponent, _extends({}, tooltipArrowProps, {\n            ref: setArrowRef\n          })) : null]\n        }))\n      }))\n    }))]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Tooltip.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, adds an arrow to the tooltip.\n   * @default false\n   */\n  arrow: PropTypes.bool,\n  /**\n   * Tooltip reference element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Arrow: PropTypes.elementType,\n    Popper: PropTypes.elementType,\n    Tooltip: PropTypes.elementType,\n    Transition: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    arrow: PropTypes.object,\n    popper: PropTypes.object,\n    tooltip: PropTypes.object,\n    transition: PropTypes.object\n  }),\n  /**\n   * Set to `true` if the `title` acts as an accessible description.\n   * By default the `title` acts as an accessible label for the child.\n   * @default false\n   */\n  describeChild: PropTypes.bool,\n  /**\n   * Do not respond to focus-visible events.\n   * @default false\n   */\n  disableFocusListener: PropTypes.bool,\n  /**\n   * Do not respond to hover events.\n   * @default false\n   */\n  disableHoverListener: PropTypes.bool,\n  /**\n   * Makes a tooltip not interactive, i.e. it will close when the user\n   * hovers over the tooltip before the `leaveDelay` is expired.\n   * @default false\n   */\n  disableInteractive: PropTypes.bool,\n  /**\n   * Do not respond to long press touch events.\n   * @default false\n   */\n  disableTouchListener: PropTypes.bool,\n  /**\n   * The number of milliseconds to wait before showing the tooltip.\n   * This prop won't impact the enter touch delay (`enterTouchDelay`).\n   * @default 100\n   */\n  enterDelay: PropTypes.number,\n  /**\n   * The number of milliseconds to wait before showing the tooltip when one was already recently opened.\n   * @default 0\n   */\n  enterNextDelay: PropTypes.number,\n  /**\n   * The number of milliseconds a user must touch the element before showing the tooltip.\n   * @default 700\n   */\n  enterTouchDelay: PropTypes.number,\n  /**\n   * If `true`, the tooltip follow the cursor over the wrapped element.\n   * @default false\n   */\n  followCursor: PropTypes.bool,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide this prop. It falls back to a randomly generated id.\n   */\n  id: PropTypes.string,\n  /**\n   * The number of milliseconds to wait before hiding the tooltip.\n   * This prop won't impact the leave touch delay (`leaveTouchDelay`).\n   * @default 0\n   */\n  leaveDelay: PropTypes.number,\n  /**\n   * The number of milliseconds after the user stops touching an element before hiding the tooltip.\n   * @default 1500\n   */\n  leaveTouchDelay: PropTypes.number,\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be open.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * Tooltip placement.\n   * @default 'bottom'\n   */\n  placement: PropTypes.oneOf(['bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n  /**\n   * The component used for the popper.\n   * @default Popper\n   */\n  PopperComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Popper`](/material-ui/api/popper/) element.\n   * @default {}\n   */\n  PopperProps: PropTypes.object,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    arrow: PropTypes.object,\n    popper: PropTypes.object,\n    tooltip: PropTypes.object,\n    transition: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `components` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    arrow: PropTypes.elementType,\n    popper: PropTypes.elementType,\n    tooltip: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Tooltip title. Zero-length titles string, undefined, null and false are never displayed.\n   */\n  title: PropTypes.node,\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Grow\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Tooltip;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "useTimeout", "Timeout", "elementAcceptingRef", "composeClasses", "alpha", "useRtl", "appendOwnerState", "getReactElementRef", "styled", "useTheme", "useDefaultProps", "capitalize", "Grow", "<PERSON><PERSON>", "useEventCallback", "useForkRef", "useId", "useIsFocusVisible", "useControlled", "tooltipClasses", "getTooltipUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "round", "value", "Math", "useUtilityClasses", "ownerState", "classes", "disableInteractive", "arrow", "touch", "placement", "slots", "popper", "tooltip", "split", "TooltipPopper", "name", "slot", "overridesResolver", "props", "styles", "popperInteractive", "popperArrow", "open", "popperClose", "theme", "zIndex", "vars", "pointerEvents", "top", "marginTop", "transform<PERSON><PERSON>in", "bottom", "marginBottom", "isRtl", "left", "marginLeft", "right", "marginRight", "height", "width", "TooltipTooltip", "tooltipArrow", "backgroundColor", "palette", "<PERSON><PERSON><PERSON>", "bg", "grey", "borderRadius", "shape", "color", "common", "white", "fontFamily", "typography", "padding", "fontSize", "pxToRem", "max<PERSON><PERSON><PERSON>", "margin", "wordWrap", "fontWeight", "fontWeightMedium", "position", "lineHeight", "fontWeightRegular", "TooltipArrow", "overflow", "boxSizing", "content", "display", "transform", "hystersisOpen", "hystersis<PERSON><PERSON>r", "cursorPosition", "x", "y", "testReset", "clear", "composeEventHandler", "handler", "<PERSON><PERSON><PERSON><PERSON>", "event", "params", "forwardRef", "inProps", "ref", "_ref", "_slots$popper", "_ref2", "_ref3", "_slots$transition", "_ref4", "_slots$tooltip", "_ref5", "_slots$arrow", "_slotProps$popper", "_ref6", "_slotProps$popper2", "_slotProps$transition", "_slotProps$tooltip", "_ref7", "_slotProps$tooltip2", "_slotProps$arrow", "_ref8", "_slotProps$arrow2", "children", "childrenProp", "components", "componentsProps", "<PERSON><PERSON><PERSON><PERSON>", "disableFocusListener", "disableHoverListener", "disableInteractiveProp", "disableTouch<PERSON><PERSON>ener", "enterDelay", "enterNextDelay", "enterTouchDelay", "followCursor", "id", "idProp", "leaveDelay", "leaveTouchDelay", "onClose", "onOpen", "openProp", "PopperComponent", "PopperComponentProp", "PopperProps", "slotProps", "title", "TransitionComponent", "TransitionComponentProp", "TransitionProps", "other", "isValidElement", "childNode", "setChildNode", "useState", "arrowRef", "setArrowRef", "ignoreNonTouchEvents", "useRef", "closeTimer", "enterTimer", "leaveTimer", "touchTimer", "openState", "setOpenState", "controlled", "default", "state", "process", "env", "NODE_ENV", "current", "isControlled", "undefined", "useEffect", "disabled", "tagName", "toLowerCase", "console", "error", "join", "prevUserSelect", "stopTouchInteraction", "document", "body", "style", "WebkitUserSelect", "handleOpen", "handleClose", "start", "transitions", "duration", "shortest", "handleMouseOver", "type", "removeAttribute", "handleMouseLeave", "isFocusVisibleRef", "onBlur", "handleBlurVisible", "onFocus", "handleFocusVisible", "focusVisibleRef", "setChildIsFocusVisible", "handleBlur", "handleFocus", "currentTarget", "detectTouchStart", "childrenProps", "onTouchStart", "handleTouchStart", "handleTouchEnd", "onTouchEnd", "handleKeyDown", "nativeEvent", "key", "addEventListener", "removeEventListener", "handleRef", "popperRef", "handleMouseMove", "onMouseMove", "clientX", "clientY", "update", "nameOrDescProps", "titleIsString", "className", "getAttribute", "interactiveWrapperListeners", "onMouseOver", "onMouseLeave", "popperOptions", "useMemo", "_PopperProps$popperOp", "tooltipModifiers", "enabled", "Boolean", "options", "element", "modifiers", "concat", "transition", "Transition", "TooltipComponent", "ArrowComponent", "Arrow", "popperProps", "transitionProps", "tooltipProps", "tooltipArrowProps", "Fragment", "cloneElement", "as", "anchorEl", "getBoundingClientRect", "TransitionPropsInner", "timeout", "shorter", "propTypes", "bool", "isRequired", "object", "string", "elementType", "number", "func", "oneOf", "sx", "oneOfType", "arrayOf", "node"], "sources": ["D:/Safety Tracking Web App/node_modules/@mui/material/Tooltip/Tooltip.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"arrow\", \"children\", \"classes\", \"components\", \"componentsProps\", \"describeChild\", \"disableFocusListener\", \"disableHoverListener\", \"disableInteractive\", \"disableTouchListener\", \"enterDelay\", \"enterNextDelay\", \"enterTouchDelay\", \"followCursor\", \"id\", \"leaveDelay\", \"leaveTouchDelay\", \"onClose\", \"onOpen\", \"open\", \"placement\", \"PopperComponent\", \"PopperProps\", \"slotProps\", \"slots\", \"title\", \"TransitionComponent\", \"TransitionProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport useTimeout, { Timeout } from '@mui/utils/useTimeout';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport appendOwnerState from '@mui/utils/appendOwnerState';\nimport getReactElementRef from '@mui/utils/getReactElementRef';\nimport { styled, useTheme } from '../styles';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport capitalize from '../utils/capitalize';\nimport Grow from '../Grow';\nimport Popper from '../Popper';\nimport useEventCallback from '../utils/useEventCallback';\nimport useForkRef from '../utils/useForkRef';\nimport useId from '../utils/useId';\nimport useIsFocusVisible from '../utils/useIsFocusVisible';\nimport useControlled from '../utils/useControlled';\nimport tooltipClasses, { getTooltipUtilityClass } from './tooltipClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction round(value) {\n  return Math.round(value * 1e5) / 1e5;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableInteractive,\n    arrow,\n    touch,\n    placement\n  } = ownerState;\n  const slots = {\n    popper: ['popper', !disableInteractive && 'popperInteractive', arrow && 'popperArrow'],\n    tooltip: ['tooltip', arrow && 'tooltipArrow', touch && 'touch', `tooltipPlacement${capitalize(placement.split('-')[0])}`],\n    arrow: ['arrow']\n  };\n  return composeClasses(slots, getTooltipUtilityClass, classes);\n};\nconst TooltipPopper = styled(Popper, {\n  name: 'MuiTooltip',\n  slot: 'Popper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.popper, !ownerState.disableInteractive && styles.popperInteractive, ownerState.arrow && styles.popperArrow, !ownerState.open && styles.popperClose];\n  }\n})(({\n  theme,\n  ownerState,\n  open\n}) => _extends({\n  zIndex: (theme.vars || theme).zIndex.tooltip,\n  pointerEvents: 'none'\n}, !ownerState.disableInteractive && {\n  pointerEvents: 'auto'\n}, !open && {\n  pointerEvents: 'none'\n}, ownerState.arrow && {\n  [`&[data-popper-placement*=\"bottom\"] .${tooltipClasses.arrow}`]: {\n    top: 0,\n    marginTop: '-0.71em',\n    '&::before': {\n      transformOrigin: '0 100%'\n    }\n  },\n  [`&[data-popper-placement*=\"top\"] .${tooltipClasses.arrow}`]: {\n    bottom: 0,\n    marginBottom: '-0.71em',\n    '&::before': {\n      transformOrigin: '100% 0'\n    }\n  },\n  [`&[data-popper-placement*=\"right\"] .${tooltipClasses.arrow}`]: _extends({}, !ownerState.isRtl ? {\n    left: 0,\n    marginLeft: '-0.71em'\n  } : {\n    right: 0,\n    marginRight: '-0.71em'\n  }, {\n    height: '1em',\n    width: '0.71em',\n    '&::before': {\n      transformOrigin: '100% 100%'\n    }\n  }),\n  [`&[data-popper-placement*=\"left\"] .${tooltipClasses.arrow}`]: _extends({}, !ownerState.isRtl ? {\n    right: 0,\n    marginRight: '-0.71em'\n  } : {\n    left: 0,\n    marginLeft: '-0.71em'\n  }, {\n    height: '1em',\n    width: '0.71em',\n    '&::before': {\n      transformOrigin: '0 0'\n    }\n  })\n}));\nconst TooltipTooltip = styled('div', {\n  name: 'MuiTooltip',\n  slot: 'Tooltip',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.tooltip, ownerState.touch && styles.touch, ownerState.arrow && styles.tooltipArrow, styles[`tooltipPlacement${capitalize(ownerState.placement.split('-')[0])}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  backgroundColor: theme.vars ? theme.vars.palette.Tooltip.bg : alpha(theme.palette.grey[700], 0.92),\n  borderRadius: (theme.vars || theme).shape.borderRadius,\n  color: (theme.vars || theme).palette.common.white,\n  fontFamily: theme.typography.fontFamily,\n  padding: '4px 8px',\n  fontSize: theme.typography.pxToRem(11),\n  maxWidth: 300,\n  margin: 2,\n  wordWrap: 'break-word',\n  fontWeight: theme.typography.fontWeightMedium\n}, ownerState.arrow && {\n  position: 'relative',\n  margin: 0\n}, ownerState.touch && {\n  padding: '8px 16px',\n  fontSize: theme.typography.pxToRem(14),\n  lineHeight: `${round(16 / 14)}em`,\n  fontWeight: theme.typography.fontWeightRegular\n}, {\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"left\"] &`]: _extends({\n    transformOrigin: 'right center'\n  }, !ownerState.isRtl ? _extends({\n    marginRight: '14px'\n  }, ownerState.touch && {\n    marginRight: '24px'\n  }) : _extends({\n    marginLeft: '14px'\n  }, ownerState.touch && {\n    marginLeft: '24px'\n  })),\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"right\"] &`]: _extends({\n    transformOrigin: 'left center'\n  }, !ownerState.isRtl ? _extends({\n    marginLeft: '14px'\n  }, ownerState.touch && {\n    marginLeft: '24px'\n  }) : _extends({\n    marginRight: '14px'\n  }, ownerState.touch && {\n    marginRight: '24px'\n  })),\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"top\"] &`]: _extends({\n    transformOrigin: 'center bottom',\n    marginBottom: '14px'\n  }, ownerState.touch && {\n    marginBottom: '24px'\n  }),\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"bottom\"] &`]: _extends({\n    transformOrigin: 'center top',\n    marginTop: '14px'\n  }, ownerState.touch && {\n    marginTop: '24px'\n  })\n}));\nconst TooltipArrow = styled('span', {\n  name: 'MuiTooltip',\n  slot: 'Arrow',\n  overridesResolver: (props, styles) => styles.arrow\n})(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  position: 'absolute',\n  width: '1em',\n  height: '0.71em' /* = width / sqrt(2) = (length of the hypotenuse) */,\n  boxSizing: 'border-box',\n  color: theme.vars ? theme.vars.palette.Tooltip.bg : alpha(theme.palette.grey[700], 0.9),\n  '&::before': {\n    content: '\"\"',\n    margin: 'auto',\n    display: 'block',\n    width: '100%',\n    height: '100%',\n    backgroundColor: 'currentColor',\n    transform: 'rotate(45deg)'\n  }\n}));\nlet hystersisOpen = false;\nconst hystersisTimer = new Timeout();\nlet cursorPosition = {\n  x: 0,\n  y: 0\n};\nexport function testReset() {\n  hystersisOpen = false;\n  hystersisTimer.clear();\n}\nfunction composeEventHandler(handler, eventHandler) {\n  return (event, ...params) => {\n    if (eventHandler) {\n      eventHandler(event, ...params);\n    }\n    handler(event, ...params);\n  };\n}\n\n// TODO v6: Remove PopperComponent, PopperProps, TransitionComponent and TransitionProps.\nconst Tooltip = /*#__PURE__*/React.forwardRef(function Tooltip(inProps, ref) {\n  var _ref, _slots$popper, _ref2, _ref3, _slots$transition, _ref4, _slots$tooltip, _ref5, _slots$arrow, _slotProps$popper, _ref6, _slotProps$popper2, _slotProps$transition, _slotProps$tooltip, _ref7, _slotProps$tooltip2, _slotProps$arrow, _ref8, _slotProps$arrow2;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTooltip'\n  });\n  const {\n      arrow = false,\n      children: childrenProp,\n      components = {},\n      componentsProps = {},\n      describeChild = false,\n      disableFocusListener = false,\n      disableHoverListener = false,\n      disableInteractive: disableInteractiveProp = false,\n      disableTouchListener = false,\n      enterDelay = 100,\n      enterNextDelay = 0,\n      enterTouchDelay = 700,\n      followCursor = false,\n      id: idProp,\n      leaveDelay = 0,\n      leaveTouchDelay = 1500,\n      onClose,\n      onOpen,\n      open: openProp,\n      placement = 'bottom',\n      PopperComponent: PopperComponentProp,\n      PopperProps = {},\n      slotProps = {},\n      slots = {},\n      title,\n      TransitionComponent: TransitionComponentProp = Grow,\n      TransitionProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  // to prevent runtime errors, developers will need to provide a child as a React element anyway.\n  const children = /*#__PURE__*/React.isValidElement(childrenProp) ? childrenProp : /*#__PURE__*/_jsx(\"span\", {\n    children: childrenProp\n  });\n  const theme = useTheme();\n  const isRtl = useRtl();\n  const [childNode, setChildNode] = React.useState();\n  const [arrowRef, setArrowRef] = React.useState(null);\n  const ignoreNonTouchEvents = React.useRef(false);\n  const disableInteractive = disableInteractiveProp || followCursor;\n  const closeTimer = useTimeout();\n  const enterTimer = useTimeout();\n  const leaveTimer = useTimeout();\n  const touchTimer = useTimeout();\n  const [openState, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: 'Tooltip',\n    state: 'open'\n  });\n  let open = openState;\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const {\n      current: isControlled\n    } = React.useRef(openProp !== undefined);\n\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (childNode && childNode.disabled && !isControlled && title !== '' && childNode.tagName.toLowerCase() === 'button') {\n        console.error(['MUI: You are providing a disabled `button` child to the Tooltip component.', 'A disabled element does not fire events.', \"Tooltip needs to listen to the child element's events to display the title.\", '', 'Add a simple wrapper element, such as a `span`.'].join('\\n'));\n      }\n    }, [title, childNode, isControlled]);\n  }\n  const id = useId(idProp);\n  const prevUserSelect = React.useRef();\n  const stopTouchInteraction = useEventCallback(() => {\n    if (prevUserSelect.current !== undefined) {\n      document.body.style.WebkitUserSelect = prevUserSelect.current;\n      prevUserSelect.current = undefined;\n    }\n    touchTimer.clear();\n  });\n  React.useEffect(() => stopTouchInteraction, [stopTouchInteraction]);\n  const handleOpen = event => {\n    hystersisTimer.clear();\n    hystersisOpen = true;\n\n    // The mouseover event will trigger for every nested element in the tooltip.\n    // We can skip rerendering when the tooltip is already open.\n    // We are using the mouseover event instead of the mouseenter event to fix a hide/show issue.\n    setOpenState(true);\n    if (onOpen && !open) {\n      onOpen(event);\n    }\n  };\n  const handleClose = useEventCallback(\n  /**\n   * @param {React.SyntheticEvent | Event} event\n   */\n  event => {\n    hystersisTimer.start(800 + leaveDelay, () => {\n      hystersisOpen = false;\n    });\n    setOpenState(false);\n    if (onClose && open) {\n      onClose(event);\n    }\n    closeTimer.start(theme.transitions.duration.shortest, () => {\n      ignoreNonTouchEvents.current = false;\n    });\n  });\n  const handleMouseOver = event => {\n    if (ignoreNonTouchEvents.current && event.type !== 'touchstart') {\n      return;\n    }\n\n    // Remove the title ahead of time.\n    // We don't want to wait for the next render commit.\n    // We would risk displaying two tooltips at the same time (native + this one).\n    if (childNode) {\n      childNode.removeAttribute('title');\n    }\n    enterTimer.clear();\n    leaveTimer.clear();\n    if (enterDelay || hystersisOpen && enterNextDelay) {\n      enterTimer.start(hystersisOpen ? enterNextDelay : enterDelay, () => {\n        handleOpen(event);\n      });\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleMouseLeave = event => {\n    enterTimer.clear();\n    leaveTimer.start(leaveDelay, () => {\n      handleClose(event);\n    });\n  };\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  // We don't necessarily care about the focusVisible state (which is safe to access via ref anyway).\n  // We just need to re-render the Tooltip if the focus-visible state changes.\n  const [, setChildIsFocusVisible] = React.useState(false);\n  const handleBlur = event => {\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setChildIsFocusVisible(false);\n      handleMouseLeave(event);\n    }\n  };\n  const handleFocus = event => {\n    // Workaround for https://github.com/facebook/react/issues/7769\n    // The autoFocus of React might trigger the event before the componentDidMount.\n    // We need to account for this eventuality.\n    if (!childNode) {\n      setChildNode(event.currentTarget);\n    }\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setChildIsFocusVisible(true);\n      handleMouseOver(event);\n    }\n  };\n  const detectTouchStart = event => {\n    ignoreNonTouchEvents.current = true;\n    const childrenProps = children.props;\n    if (childrenProps.onTouchStart) {\n      childrenProps.onTouchStart(event);\n    }\n  };\n  const handleTouchStart = event => {\n    detectTouchStart(event);\n    leaveTimer.clear();\n    closeTimer.clear();\n    stopTouchInteraction();\n    prevUserSelect.current = document.body.style.WebkitUserSelect;\n    // Prevent iOS text selection on long-tap.\n    document.body.style.WebkitUserSelect = 'none';\n    touchTimer.start(enterTouchDelay, () => {\n      document.body.style.WebkitUserSelect = prevUserSelect.current;\n      handleMouseOver(event);\n    });\n  };\n  const handleTouchEnd = event => {\n    if (children.props.onTouchEnd) {\n      children.props.onTouchEnd(event);\n    }\n    stopTouchInteraction();\n    leaveTimer.start(leaveTouchDelay, () => {\n      handleClose(event);\n    });\n  };\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n\n    /**\n     * @param {KeyboardEvent} nativeEvent\n     */\n    function handleKeyDown(nativeEvent) {\n      // IE11, Edge (prior to using Bink?) use 'Esc'\n      if (nativeEvent.key === 'Escape' || nativeEvent.key === 'Esc') {\n        handleClose(nativeEvent);\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [handleClose, open]);\n  const handleRef = useForkRef(getReactElementRef(children), focusVisibleRef, setChildNode, ref);\n\n  // There is no point in displaying an empty tooltip.\n  // So we exclude all falsy values, except 0, which is valid.\n  if (!title && title !== 0) {\n    open = false;\n  }\n  const popperRef = React.useRef();\n  const handleMouseMove = event => {\n    const childrenProps = children.props;\n    if (childrenProps.onMouseMove) {\n      childrenProps.onMouseMove(event);\n    }\n    cursorPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    if (popperRef.current) {\n      popperRef.current.update();\n    }\n  };\n  const nameOrDescProps = {};\n  const titleIsString = typeof title === 'string';\n  if (describeChild) {\n    nameOrDescProps.title = !open && titleIsString && !disableHoverListener ? title : null;\n    nameOrDescProps['aria-describedby'] = open ? id : null;\n  } else {\n    nameOrDescProps['aria-label'] = titleIsString ? title : null;\n    nameOrDescProps['aria-labelledby'] = open && !titleIsString ? id : null;\n  }\n  const childrenProps = _extends({}, nameOrDescProps, other, children.props, {\n    className: clsx(other.className, children.props.className),\n    onTouchStart: detectTouchStart,\n    ref: handleRef\n  }, followCursor ? {\n    onMouseMove: handleMouseMove\n  } : {});\n  if (process.env.NODE_ENV !== 'production') {\n    childrenProps['data-mui-internal-clone-element'] = true;\n\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (childNode && !childNode.getAttribute('data-mui-internal-clone-element')) {\n        console.error(['MUI: The `children` component of the Tooltip is not forwarding its props correctly.', 'Please make sure that props are spread on the same element that the ref is applied to.'].join('\\n'));\n      }\n    }, [childNode]);\n  }\n  const interactiveWrapperListeners = {};\n  if (!disableTouchListener) {\n    childrenProps.onTouchStart = handleTouchStart;\n    childrenProps.onTouchEnd = handleTouchEnd;\n  }\n  if (!disableHoverListener) {\n    childrenProps.onMouseOver = composeEventHandler(handleMouseOver, childrenProps.onMouseOver);\n    childrenProps.onMouseLeave = composeEventHandler(handleMouseLeave, childrenProps.onMouseLeave);\n    if (!disableInteractive) {\n      interactiveWrapperListeners.onMouseOver = handleMouseOver;\n      interactiveWrapperListeners.onMouseLeave = handleMouseLeave;\n    }\n  }\n  if (!disableFocusListener) {\n    childrenProps.onFocus = composeEventHandler(handleFocus, childrenProps.onFocus);\n    childrenProps.onBlur = composeEventHandler(handleBlur, childrenProps.onBlur);\n    if (!disableInteractive) {\n      interactiveWrapperListeners.onFocus = handleFocus;\n      interactiveWrapperListeners.onBlur = handleBlur;\n    }\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (children.props.title) {\n      console.error(['MUI: You have provided a `title` prop to the child of <Tooltip />.', `Remove this title prop \\`${children.props.title}\\` or the Tooltip component.`].join('\\n'));\n    }\n  }\n  const popperOptions = React.useMemo(() => {\n    var _PopperProps$popperOp;\n    let tooltipModifiers = [{\n      name: 'arrow',\n      enabled: Boolean(arrowRef),\n      options: {\n        element: arrowRef,\n        padding: 4\n      }\n    }];\n    if ((_PopperProps$popperOp = PopperProps.popperOptions) != null && _PopperProps$popperOp.modifiers) {\n      tooltipModifiers = tooltipModifiers.concat(PopperProps.popperOptions.modifiers);\n    }\n    return _extends({}, PopperProps.popperOptions, {\n      modifiers: tooltipModifiers\n    });\n  }, [arrowRef, PopperProps]);\n  const ownerState = _extends({}, props, {\n    isRtl,\n    arrow,\n    disableInteractive,\n    placement,\n    PopperComponentProp,\n    touch: ignoreNonTouchEvents.current\n  });\n  const classes = useUtilityClasses(ownerState);\n  const PopperComponent = (_ref = (_slots$popper = slots.popper) != null ? _slots$popper : components.Popper) != null ? _ref : TooltipPopper;\n  const TransitionComponent = (_ref2 = (_ref3 = (_slots$transition = slots.transition) != null ? _slots$transition : components.Transition) != null ? _ref3 : TransitionComponentProp) != null ? _ref2 : Grow;\n  const TooltipComponent = (_ref4 = (_slots$tooltip = slots.tooltip) != null ? _slots$tooltip : components.Tooltip) != null ? _ref4 : TooltipTooltip;\n  const ArrowComponent = (_ref5 = (_slots$arrow = slots.arrow) != null ? _slots$arrow : components.Arrow) != null ? _ref5 : TooltipArrow;\n  const popperProps = appendOwnerState(PopperComponent, _extends({}, PopperProps, (_slotProps$popper = slotProps.popper) != null ? _slotProps$popper : componentsProps.popper, {\n    className: clsx(classes.popper, PopperProps == null ? void 0 : PopperProps.className, (_ref6 = (_slotProps$popper2 = slotProps.popper) != null ? _slotProps$popper2 : componentsProps.popper) == null ? void 0 : _ref6.className)\n  }), ownerState);\n  const transitionProps = appendOwnerState(TransitionComponent, _extends({}, TransitionProps, (_slotProps$transition = slotProps.transition) != null ? _slotProps$transition : componentsProps.transition), ownerState);\n  const tooltipProps = appendOwnerState(TooltipComponent, _extends({}, (_slotProps$tooltip = slotProps.tooltip) != null ? _slotProps$tooltip : componentsProps.tooltip, {\n    className: clsx(classes.tooltip, (_ref7 = (_slotProps$tooltip2 = slotProps.tooltip) != null ? _slotProps$tooltip2 : componentsProps.tooltip) == null ? void 0 : _ref7.className)\n  }), ownerState);\n  const tooltipArrowProps = appendOwnerState(ArrowComponent, _extends({}, (_slotProps$arrow = slotProps.arrow) != null ? _slotProps$arrow : componentsProps.arrow, {\n    className: clsx(classes.arrow, (_ref8 = (_slotProps$arrow2 = slotProps.arrow) != null ? _slotProps$arrow2 : componentsProps.arrow) == null ? void 0 : _ref8.className)\n  }), ownerState);\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/React.cloneElement(children, childrenProps), /*#__PURE__*/_jsx(PopperComponent, _extends({\n      as: PopperComponentProp != null ? PopperComponentProp : Popper,\n      placement: placement,\n      anchorEl: followCursor ? {\n        getBoundingClientRect: () => ({\n          top: cursorPosition.y,\n          left: cursorPosition.x,\n          right: cursorPosition.x,\n          bottom: cursorPosition.y,\n          width: 0,\n          height: 0\n        })\n      } : childNode,\n      popperRef: popperRef,\n      open: childNode ? open : false,\n      id: id,\n      transition: true\n    }, interactiveWrapperListeners, popperProps, {\n      popperOptions: popperOptions,\n      children: ({\n        TransitionProps: TransitionPropsInner\n      }) => /*#__PURE__*/_jsx(TransitionComponent, _extends({\n        timeout: theme.transitions.duration.shorter\n      }, TransitionPropsInner, transitionProps, {\n        children: /*#__PURE__*/_jsxs(TooltipComponent, _extends({}, tooltipProps, {\n          children: [title, arrow ? /*#__PURE__*/_jsx(ArrowComponent, _extends({}, tooltipArrowProps, {\n            ref: setArrowRef\n          })) : null]\n        }))\n      }))\n    }))]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Tooltip.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, adds an arrow to the tooltip.\n   * @default false\n   */\n  arrow: PropTypes.bool,\n  /**\n   * Tooltip reference element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Arrow: PropTypes.elementType,\n    Popper: PropTypes.elementType,\n    Tooltip: PropTypes.elementType,\n    Transition: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    arrow: PropTypes.object,\n    popper: PropTypes.object,\n    tooltip: PropTypes.object,\n    transition: PropTypes.object\n  }),\n  /**\n   * Set to `true` if the `title` acts as an accessible description.\n   * By default the `title` acts as an accessible label for the child.\n   * @default false\n   */\n  describeChild: PropTypes.bool,\n  /**\n   * Do not respond to focus-visible events.\n   * @default false\n   */\n  disableFocusListener: PropTypes.bool,\n  /**\n   * Do not respond to hover events.\n   * @default false\n   */\n  disableHoverListener: PropTypes.bool,\n  /**\n   * Makes a tooltip not interactive, i.e. it will close when the user\n   * hovers over the tooltip before the `leaveDelay` is expired.\n   * @default false\n   */\n  disableInteractive: PropTypes.bool,\n  /**\n   * Do not respond to long press touch events.\n   * @default false\n   */\n  disableTouchListener: PropTypes.bool,\n  /**\n   * The number of milliseconds to wait before showing the tooltip.\n   * This prop won't impact the enter touch delay (`enterTouchDelay`).\n   * @default 100\n   */\n  enterDelay: PropTypes.number,\n  /**\n   * The number of milliseconds to wait before showing the tooltip when one was already recently opened.\n   * @default 0\n   */\n  enterNextDelay: PropTypes.number,\n  /**\n   * The number of milliseconds a user must touch the element before showing the tooltip.\n   * @default 700\n   */\n  enterTouchDelay: PropTypes.number,\n  /**\n   * If `true`, the tooltip follow the cursor over the wrapped element.\n   * @default false\n   */\n  followCursor: PropTypes.bool,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide this prop. It falls back to a randomly generated id.\n   */\n  id: PropTypes.string,\n  /**\n   * The number of milliseconds to wait before hiding the tooltip.\n   * This prop won't impact the leave touch delay (`leaveTouchDelay`).\n   * @default 0\n   */\n  leaveDelay: PropTypes.number,\n  /**\n   * The number of milliseconds after the user stops touching an element before hiding the tooltip.\n   * @default 1500\n   */\n  leaveTouchDelay: PropTypes.number,\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be open.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * Tooltip placement.\n   * @default 'bottom'\n   */\n  placement: PropTypes.oneOf(['bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n  /**\n   * The component used for the popper.\n   * @default Popper\n   */\n  PopperComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Popper`](/material-ui/api/popper/) element.\n   * @default {}\n   */\n  PopperProps: PropTypes.object,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    arrow: PropTypes.object,\n    popper: PropTypes.object,\n    tooltip: PropTypes.object,\n    transition: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `components` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    arrow: PropTypes.elementType,\n    popper: PropTypes.elementType,\n    tooltip: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Tooltip title. Zero-length titles string, undefined, null and false are never displayed.\n   */\n  title: PropTypes.node,\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Grow\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Tooltip;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,iBAAiB,EAAE,eAAe,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,cAAc,EAAE,IAAI,EAAE,YAAY,EAAE,iBAAiB,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,iBAAiB,EAAE,aAAa,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,qBAAqB,EAAE,iBAAiB,CAAC;AACjc,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,UAAU,IAAIC,OAAO,QAAQ,uBAAuB;AAC3D,OAAOC,mBAAmB,MAAM,gCAAgC;AAChE,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,KAAK,QAAQ,8BAA8B;AACpD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,kBAAkB,MAAM,+BAA+B;AAC9D,SAASC,MAAM,EAAEC,QAAQ,QAAQ,WAAW;AAC5C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,KAAK,MAAM,gBAAgB;AAClC,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,cAAc,IAAIC,sBAAsB,QAAQ,kBAAkB;AACzE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,SAASC,KAAKA,CAACC,KAAK,EAAE;EACpB,OAAOC,IAAI,CAACF,KAAK,CAACC,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG;AACtC;AACA,MAAME,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,kBAAkB;IAClBC,KAAK;IACLC,KAAK;IACLC;EACF,CAAC,GAAGL,UAAU;EACd,MAAMM,KAAK,GAAG;IACZC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAACL,kBAAkB,IAAI,mBAAmB,EAAEC,KAAK,IAAI,aAAa,CAAC;IACtFK,OAAO,EAAE,CAAC,SAAS,EAAEL,KAAK,IAAI,cAAc,EAAEC,KAAK,IAAI,OAAO,EAAE,mBAAmBtB,UAAU,CAACuB,SAAS,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzHN,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,OAAO7B,cAAc,CAACgC,KAAK,EAAEf,sBAAsB,EAAEU,OAAO,CAAC;AAC/D,CAAC;AACD,MAAMS,aAAa,GAAG/B,MAAM,CAACK,MAAM,EAAE;EACnC2B,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,QAAQ;EACdC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJf;IACF,CAAC,GAAGc,KAAK;IACT,OAAO,CAACC,MAAM,CAACR,MAAM,EAAE,CAACP,UAAU,CAACE,kBAAkB,IAAIa,MAAM,CAACC,iBAAiB,EAAEhB,UAAU,CAACG,KAAK,IAAIY,MAAM,CAACE,WAAW,EAAE,CAACjB,UAAU,CAACkB,IAAI,IAAIH,MAAM,CAACI,WAAW,CAAC;EACpK;AACF,CAAC,CAAC,CAAC,CAAC;EACFC,KAAK;EACLpB,UAAU;EACVkB;AACF,CAAC,KAAKpD,QAAQ,CAAC;EACbuD,MAAM,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEC,MAAM,CAACb,OAAO;EAC5Ce,aAAa,EAAE;AACjB,CAAC,EAAE,CAACvB,UAAU,CAACE,kBAAkB,IAAI;EACnCqB,aAAa,EAAE;AACjB,CAAC,EAAE,CAACL,IAAI,IAAI;EACVK,aAAa,EAAE;AACjB,CAAC,EAAEvB,UAAU,CAACG,KAAK,IAAI;EACrB,CAAC,uCAAuCb,cAAc,CAACa,KAAK,EAAE,GAAG;IAC/DqB,GAAG,EAAE,CAAC;IACNC,SAAS,EAAE,SAAS;IACpB,WAAW,EAAE;MACXC,eAAe,EAAE;IACnB;EACF,CAAC;EACD,CAAC,oCAAoCpC,cAAc,CAACa,KAAK,EAAE,GAAG;IAC5DwB,MAAM,EAAE,CAAC;IACTC,YAAY,EAAE,SAAS;IACvB,WAAW,EAAE;MACXF,eAAe,EAAE;IACnB;EACF,CAAC;EACD,CAAC,sCAAsCpC,cAAc,CAACa,KAAK,EAAE,GAAGrC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAACkC,UAAU,CAAC6B,KAAK,GAAG;IAC/FC,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE;EACd,CAAC,GAAG;IACFC,KAAK,EAAE,CAAC;IACRC,WAAW,EAAE;EACf,CAAC,EAAE;IACDC,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,QAAQ;IACf,WAAW,EAAE;MACXT,eAAe,EAAE;IACnB;EACF,CAAC,CAAC;EACF,CAAC,qCAAqCpC,cAAc,CAACa,KAAK,EAAE,GAAGrC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAACkC,UAAU,CAAC6B,KAAK,GAAG;IAC9FG,KAAK,EAAE,CAAC;IACRC,WAAW,EAAE;EACf,CAAC,GAAG;IACFH,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE;EACd,CAAC,EAAE;IACDG,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,QAAQ;IACf,WAAW,EAAE;MACXT,eAAe,EAAE;IACnB;EACF,CAAC;AACH,CAAC,CAAC,CAAC;AACH,MAAMU,cAAc,GAAGzD,MAAM,CAAC,KAAK,EAAE;EACnCgC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,SAAS;EACfC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJf;IACF,CAAC,GAAGc,KAAK;IACT,OAAO,CAACC,MAAM,CAACP,OAAO,EAAER,UAAU,CAACI,KAAK,IAAIW,MAAM,CAACX,KAAK,EAAEJ,UAAU,CAACG,KAAK,IAAIY,MAAM,CAACsB,YAAY,EAAEtB,MAAM,CAAC,mBAAmBjC,UAAU,CAACkB,UAAU,CAACK,SAAS,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACjL;AACF,CAAC,CAAC,CAAC,CAAC;EACFW,KAAK;EACLpB;AACF,CAAC,KAAKlC,QAAQ,CAAC;EACbwE,eAAe,EAAElB,KAAK,CAACE,IAAI,GAAGF,KAAK,CAACE,IAAI,CAACiB,OAAO,CAACC,OAAO,CAACC,EAAE,GAAGlE,KAAK,CAAC6C,KAAK,CAACmB,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;EAClGC,YAAY,EAAE,CAACvB,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEwB,KAAK,CAACD,YAAY;EACtDE,KAAK,EAAE,CAACzB,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEmB,OAAO,CAACO,MAAM,CAACC,KAAK;EACjDC,UAAU,EAAE5B,KAAK,CAAC6B,UAAU,CAACD,UAAU;EACvCE,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE/B,KAAK,CAAC6B,UAAU,CAACG,OAAO,CAAC,EAAE,CAAC;EACtCC,QAAQ,EAAE,GAAG;EACbC,MAAM,EAAE,CAAC;EACTC,QAAQ,EAAE,YAAY;EACtBC,UAAU,EAAEpC,KAAK,CAAC6B,UAAU,CAACQ;AAC/B,CAAC,EAAEzD,UAAU,CAACG,KAAK,IAAI;EACrBuD,QAAQ,EAAE,UAAU;EACpBJ,MAAM,EAAE;AACV,CAAC,EAAEtD,UAAU,CAACI,KAAK,IAAI;EACrB8C,OAAO,EAAE,UAAU;EACnBC,QAAQ,EAAE/B,KAAK,CAAC6B,UAAU,CAACG,OAAO,CAAC,EAAE,CAAC;EACtCO,UAAU,EAAE,GAAG/D,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI;EACjC4D,UAAU,EAAEpC,KAAK,CAAC6B,UAAU,CAACW;AAC/B,CAAC,EAAE;EACD,CAAC,IAAItE,cAAc,CAACiB,MAAM,mCAAmC,GAAGzC,QAAQ,CAAC;IACvE4D,eAAe,EAAE;EACnB,CAAC,EAAE,CAAC1B,UAAU,CAAC6B,KAAK,GAAG/D,QAAQ,CAAC;IAC9BmE,WAAW,EAAE;EACf,CAAC,EAAEjC,UAAU,CAACI,KAAK,IAAI;IACrB6B,WAAW,EAAE;EACf,CAAC,CAAC,GAAGnE,QAAQ,CAAC;IACZiE,UAAU,EAAE;EACd,CAAC,EAAE/B,UAAU,CAACI,KAAK,IAAI;IACrB2B,UAAU,EAAE;EACd,CAAC,CAAC,CAAC;EACH,CAAC,IAAIzC,cAAc,CAACiB,MAAM,oCAAoC,GAAGzC,QAAQ,CAAC;IACxE4D,eAAe,EAAE;EACnB,CAAC,EAAE,CAAC1B,UAAU,CAAC6B,KAAK,GAAG/D,QAAQ,CAAC;IAC9BiE,UAAU,EAAE;EACd,CAAC,EAAE/B,UAAU,CAACI,KAAK,IAAI;IACrB2B,UAAU,EAAE;EACd,CAAC,CAAC,GAAGjE,QAAQ,CAAC;IACZmE,WAAW,EAAE;EACf,CAAC,EAAEjC,UAAU,CAACI,KAAK,IAAI;IACrB6B,WAAW,EAAE;EACf,CAAC,CAAC,CAAC;EACH,CAAC,IAAI3C,cAAc,CAACiB,MAAM,kCAAkC,GAAGzC,QAAQ,CAAC;IACtE4D,eAAe,EAAE,eAAe;IAChCE,YAAY,EAAE;EAChB,CAAC,EAAE5B,UAAU,CAACI,KAAK,IAAI;IACrBwB,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,CAAC,IAAItC,cAAc,CAACiB,MAAM,qCAAqC,GAAGzC,QAAQ,CAAC;IACzE4D,eAAe,EAAE,YAAY;IAC7BD,SAAS,EAAE;EACb,CAAC,EAAEzB,UAAU,CAACI,KAAK,IAAI;IACrBqB,SAAS,EAAE;EACb,CAAC;AACH,CAAC,CAAC,CAAC;AACH,MAAMoC,YAAY,GAAGlF,MAAM,CAAC,MAAM,EAAE;EAClCgC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACZ;AAC/C,CAAC,CAAC,CAAC,CAAC;EACFiB;AACF,CAAC,MAAM;EACL0C,QAAQ,EAAE,QAAQ;EAClBJ,QAAQ,EAAE,UAAU;EACpBvB,KAAK,EAAE,KAAK;EACZD,MAAM,EAAE,QAAQ,CAAC;EACjB6B,SAAS,EAAE,YAAY;EACvBlB,KAAK,EAAEzB,KAAK,CAACE,IAAI,GAAGF,KAAK,CAACE,IAAI,CAACiB,OAAO,CAACC,OAAO,CAACC,EAAE,GAAGlE,KAAK,CAAC6C,KAAK,CAACmB,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;EACvF,WAAW,EAAE;IACXsB,OAAO,EAAE,IAAI;IACbV,MAAM,EAAE,MAAM;IACdW,OAAO,EAAE,OAAO;IAChB9B,KAAK,EAAE,MAAM;IACbD,MAAM,EAAE,MAAM;IACdI,eAAe,EAAE,cAAc;IAC/B4B,SAAS,EAAE;EACb;AACF,CAAC,CAAC,CAAC;AACH,IAAIC,aAAa,GAAG,KAAK;AACzB,MAAMC,cAAc,GAAG,IAAIhG,OAAO,CAAC,CAAC;AACpC,IAAIiG,cAAc,GAAG;EACnBC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE;AACL,CAAC;AACD,OAAO,SAASC,SAASA,CAAA,EAAG;EAC1BL,aAAa,GAAG,KAAK;EACrBC,cAAc,CAACK,KAAK,CAAC,CAAC;AACxB;AACA,SAASC,mBAAmBA,CAACC,OAAO,EAAEC,YAAY,EAAE;EAClD,OAAO,CAACC,KAAK,EAAE,GAAGC,MAAM,KAAK;IAC3B,IAAIF,YAAY,EAAE;MAChBA,YAAY,CAACC,KAAK,EAAE,GAAGC,MAAM,CAAC;IAChC;IACAH,OAAO,CAACE,KAAK,EAAE,GAAGC,MAAM,CAAC;EAC3B,CAAC;AACH;;AAEA;AACA,MAAMtC,OAAO,GAAG,aAAaxE,KAAK,CAAC+G,UAAU,CAAC,SAASvC,OAAOA,CAACwC,OAAO,EAAEC,GAAG,EAAE;EAC3E,IAAIC,IAAI,EAAEC,aAAa,EAAEC,KAAK,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,cAAc,EAAEC,KAAK,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,KAAK,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,iBAAiB;EACrQ,MAAMtF,KAAK,GAAGjC,eAAe,CAAC;IAC5BiC,KAAK,EAAEkE,OAAO;IACdrE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFR,KAAK,GAAG,KAAK;MACbkG,QAAQ,EAAEC,YAAY;MACtBC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpBC,aAAa,GAAG,KAAK;MACrBC,oBAAoB,GAAG,KAAK;MAC5BC,oBAAoB,GAAG,KAAK;MAC5BzG,kBAAkB,EAAE0G,sBAAsB,GAAG,KAAK;MAClDC,oBAAoB,GAAG,KAAK;MAC5BC,UAAU,GAAG,GAAG;MAChBC,cAAc,GAAG,CAAC;MAClBC,eAAe,GAAG,GAAG;MACrBC,YAAY,GAAG,KAAK;MACpBC,EAAE,EAAEC,MAAM;MACVC,UAAU,GAAG,CAAC;MACdC,eAAe,GAAG,IAAI;MACtBC,OAAO;MACPC,MAAM;MACNrG,IAAI,EAAEsG,QAAQ;MACdnH,SAAS,GAAG,QAAQ;MACpBoH,eAAe,EAAEC,mBAAmB;MACpCC,WAAW,GAAG,CAAC,CAAC;MAChBC,SAAS,GAAG,CAAC,CAAC;MACdtH,KAAK,GAAG,CAAC,CAAC;MACVuH,KAAK;MACLC,mBAAmB,EAAEC,uBAAuB,GAAGhJ,IAAI;MACnDiJ;IACF,CAAC,GAAGlH,KAAK;IACTmH,KAAK,GAAGpK,6BAA6B,CAACiD,KAAK,EAAE/C,SAAS,CAAC;;EAEzD;EACA,MAAMsI,QAAQ,GAAG,aAAarI,KAAK,CAACkK,cAAc,CAAC5B,YAAY,CAAC,GAAGA,YAAY,GAAG,aAAa7G,IAAI,CAAC,MAAM,EAAE;IAC1G4G,QAAQ,EAAEC;EACZ,CAAC,CAAC;EACF,MAAMlF,KAAK,GAAGxC,QAAQ,CAAC,CAAC;EACxB,MAAMiD,KAAK,GAAGrD,MAAM,CAAC,CAAC;EACtB,MAAM,CAAC2J,SAAS,EAAEC,YAAY,CAAC,GAAGpK,KAAK,CAACqK,QAAQ,CAAC,CAAC;EAClD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvK,KAAK,CAACqK,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAMG,oBAAoB,GAAGxK,KAAK,CAACyK,MAAM,CAAC,KAAK,CAAC;EAChD,MAAMvI,kBAAkB,GAAG0G,sBAAsB,IAAIK,YAAY;EACjE,MAAMyB,UAAU,GAAGvK,UAAU,CAAC,CAAC;EAC/B,MAAMwK,UAAU,GAAGxK,UAAU,CAAC,CAAC;EAC/B,MAAMyK,UAAU,GAAGzK,UAAU,CAAC,CAAC;EAC/B,MAAM0K,UAAU,GAAG1K,UAAU,CAAC,CAAC;EAC/B,MAAM,CAAC2K,SAAS,EAAEC,YAAY,CAAC,GAAG1J,aAAa,CAAC;IAC9C2J,UAAU,EAAExB,QAAQ;IACpByB,OAAO,EAAE,KAAK;IACdtI,IAAI,EAAE,SAAS;IACfuI,KAAK,EAAE;EACT,CAAC,CAAC;EACF,IAAIhI,IAAI,GAAG4H,SAAS;EACpB,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA,MAAM;MACJC,OAAO,EAAEC;IACX,CAAC,GAAGvL,KAAK,CAACyK,MAAM,CAACjB,QAAQ,KAAKgC,SAAS,CAAC;;IAExC;IACAxL,KAAK,CAACyL,SAAS,CAAC,MAAM;MACpB,IAAItB,SAAS,IAAIA,SAAS,CAACuB,QAAQ,IAAI,CAACH,YAAY,IAAI1B,KAAK,KAAK,EAAE,IAAIM,SAAS,CAACwB,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;QACpHC,OAAO,CAACC,KAAK,CAAC,CAAC,4EAA4E,EAAE,0CAA0C,EAAE,6EAA6E,EAAE,EAAE,EAAE,iDAAiD,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC5R;IACF,CAAC,EAAE,CAAClC,KAAK,EAAEM,SAAS,EAAEoB,YAAY,CAAC,CAAC;EACtC;EACA,MAAMrC,EAAE,GAAG/H,KAAK,CAACgI,MAAM,CAAC;EACxB,MAAM6C,cAAc,GAAGhM,KAAK,CAACyK,MAAM,CAAC,CAAC;EACrC,MAAMwB,oBAAoB,GAAGhL,gBAAgB,CAAC,MAAM;IAClD,IAAI+K,cAAc,CAACV,OAAO,KAAKE,SAAS,EAAE;MACxCU,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,gBAAgB,GAAGL,cAAc,CAACV,OAAO;MAC7DU,cAAc,CAACV,OAAO,GAAGE,SAAS;IACpC;IACAX,UAAU,CAACpE,KAAK,CAAC,CAAC;EACpB,CAAC,CAAC;EACFzG,KAAK,CAACyL,SAAS,CAAC,MAAMQ,oBAAoB,EAAE,CAACA,oBAAoB,CAAC,CAAC;EACnE,MAAMK,UAAU,GAAGzF,KAAK,IAAI;IAC1BT,cAAc,CAACK,KAAK,CAAC,CAAC;IACtBN,aAAa,GAAG,IAAI;;IAEpB;IACA;IACA;IACA4E,YAAY,CAAC,IAAI,CAAC;IAClB,IAAIxB,MAAM,IAAI,CAACrG,IAAI,EAAE;MACnBqG,MAAM,CAAC1C,KAAK,CAAC;IACf;EACF,CAAC;EACD,MAAM0F,WAAW,GAAGtL,gBAAgB;EACpC;AACF;AACA;EACE4F,KAAK,IAAI;IACPT,cAAc,CAACoG,KAAK,CAAC,GAAG,GAAGpD,UAAU,EAAE,MAAM;MAC3CjD,aAAa,GAAG,KAAK;IACvB,CAAC,CAAC;IACF4E,YAAY,CAAC,KAAK,CAAC;IACnB,IAAIzB,OAAO,IAAIpG,IAAI,EAAE;MACnBoG,OAAO,CAACzC,KAAK,CAAC;IAChB;IACA6D,UAAU,CAAC8B,KAAK,CAACpJ,KAAK,CAACqJ,WAAW,CAACC,QAAQ,CAACC,QAAQ,EAAE,MAAM;MAC1DnC,oBAAoB,CAACc,OAAO,GAAG,KAAK;IACtC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMsB,eAAe,GAAG/F,KAAK,IAAI;IAC/B,IAAI2D,oBAAoB,CAACc,OAAO,IAAIzE,KAAK,CAACgG,IAAI,KAAK,YAAY,EAAE;MAC/D;IACF;;IAEA;IACA;IACA;IACA,IAAI1C,SAAS,EAAE;MACbA,SAAS,CAAC2C,eAAe,CAAC,OAAO,CAAC;IACpC;IACAnC,UAAU,CAAClE,KAAK,CAAC,CAAC;IAClBmE,UAAU,CAACnE,KAAK,CAAC,CAAC;IAClB,IAAIqC,UAAU,IAAI3C,aAAa,IAAI4C,cAAc,EAAE;MACjD4B,UAAU,CAAC6B,KAAK,CAACrG,aAAa,GAAG4C,cAAc,GAAGD,UAAU,EAAE,MAAM;QAClEwD,UAAU,CAACzF,KAAK,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLyF,UAAU,CAACzF,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAMkG,gBAAgB,GAAGlG,KAAK,IAAI;IAChC8D,UAAU,CAAClE,KAAK,CAAC,CAAC;IAClBmE,UAAU,CAAC4B,KAAK,CAACpD,UAAU,EAAE,MAAM;MACjCmD,WAAW,CAAC1F,KAAK,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC;EACD,MAAM;IACJmG,iBAAiB;IACjBC,MAAM,EAAEC,iBAAiB;IACzBC,OAAO,EAAEC,kBAAkB;IAC3BnG,GAAG,EAAEoG;EACP,CAAC,GAAGjM,iBAAiB,CAAC,CAAC;EACvB;EACA;EACA,MAAM,GAAGkM,sBAAsB,CAAC,GAAGtN,KAAK,CAACqK,QAAQ,CAAC,KAAK,CAAC;EACxD,MAAMkD,UAAU,GAAG1G,KAAK,IAAI;IAC1BqG,iBAAiB,CAACrG,KAAK,CAAC;IACxB,IAAImG,iBAAiB,CAAC1B,OAAO,KAAK,KAAK,EAAE;MACvCgC,sBAAsB,CAAC,KAAK,CAAC;MAC7BP,gBAAgB,CAAClG,KAAK,CAAC;IACzB;EACF,CAAC;EACD,MAAM2G,WAAW,GAAG3G,KAAK,IAAI;IAC3B;IACA;IACA;IACA,IAAI,CAACsD,SAAS,EAAE;MACdC,YAAY,CAACvD,KAAK,CAAC4G,aAAa,CAAC;IACnC;IACAL,kBAAkB,CAACvG,KAAK,CAAC;IACzB,IAAImG,iBAAiB,CAAC1B,OAAO,KAAK,IAAI,EAAE;MACtCgC,sBAAsB,CAAC,IAAI,CAAC;MAC5BV,eAAe,CAAC/F,KAAK,CAAC;IACxB;EACF,CAAC;EACD,MAAM6G,gBAAgB,GAAG7G,KAAK,IAAI;IAChC2D,oBAAoB,CAACc,OAAO,GAAG,IAAI;IACnC,MAAMqC,aAAa,GAAGtF,QAAQ,CAACvF,KAAK;IACpC,IAAI6K,aAAa,CAACC,YAAY,EAAE;MAC9BD,aAAa,CAACC,YAAY,CAAC/G,KAAK,CAAC;IACnC;EACF,CAAC;EACD,MAAMgH,gBAAgB,GAAGhH,KAAK,IAAI;IAChC6G,gBAAgB,CAAC7G,KAAK,CAAC;IACvB+D,UAAU,CAACnE,KAAK,CAAC,CAAC;IAClBiE,UAAU,CAACjE,KAAK,CAAC,CAAC;IAClBwF,oBAAoB,CAAC,CAAC;IACtBD,cAAc,CAACV,OAAO,GAAGY,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,gBAAgB;IAC7D;IACAH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,gBAAgB,GAAG,MAAM;IAC7CxB,UAAU,CAAC2B,KAAK,CAACxD,eAAe,EAAE,MAAM;MACtCkD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,gBAAgB,GAAGL,cAAc,CAACV,OAAO;MAC7DsB,eAAe,CAAC/F,KAAK,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMiH,cAAc,GAAGjH,KAAK,IAAI;IAC9B,IAAIwB,QAAQ,CAACvF,KAAK,CAACiL,UAAU,EAAE;MAC7B1F,QAAQ,CAACvF,KAAK,CAACiL,UAAU,CAAClH,KAAK,CAAC;IAClC;IACAoF,oBAAoB,CAAC,CAAC;IACtBrB,UAAU,CAAC4B,KAAK,CAACnD,eAAe,EAAE,MAAM;MACtCkD,WAAW,CAAC1F,KAAK,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC;EACD7G,KAAK,CAACyL,SAAS,CAAC,MAAM;IACpB,IAAI,CAACvI,IAAI,EAAE;MACT,OAAOsI,SAAS;IAClB;;IAEA;AACJ;AACA;IACI,SAASwC,aAAaA,CAACC,WAAW,EAAE;MAClC;MACA,IAAIA,WAAW,CAACC,GAAG,KAAK,QAAQ,IAAID,WAAW,CAACC,GAAG,KAAK,KAAK,EAAE;QAC7D3B,WAAW,CAAC0B,WAAW,CAAC;MAC1B;IACF;IACA/B,QAAQ,CAACiC,gBAAgB,CAAC,SAAS,EAAEH,aAAa,CAAC;IACnD,OAAO,MAAM;MACX9B,QAAQ,CAACkC,mBAAmB,CAAC,SAAS,EAAEJ,aAAa,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAACzB,WAAW,EAAErJ,IAAI,CAAC,CAAC;EACvB,MAAMmL,SAAS,GAAGnN,UAAU,CAACR,kBAAkB,CAAC2H,QAAQ,CAAC,EAAEgF,eAAe,EAAEjD,YAAY,EAAEnD,GAAG,CAAC;;EAE9F;EACA;EACA,IAAI,CAAC4C,KAAK,IAAIA,KAAK,KAAK,CAAC,EAAE;IACzB3G,IAAI,GAAG,KAAK;EACd;EACA,MAAMoL,SAAS,GAAGtO,KAAK,CAACyK,MAAM,CAAC,CAAC;EAChC,MAAM8D,eAAe,GAAG1H,KAAK,IAAI;IAC/B,MAAM8G,aAAa,GAAGtF,QAAQ,CAACvF,KAAK;IACpC,IAAI6K,aAAa,CAACa,WAAW,EAAE;MAC7Bb,aAAa,CAACa,WAAW,CAAC3H,KAAK,CAAC;IAClC;IACAR,cAAc,GAAG;MACfC,CAAC,EAAEO,KAAK,CAAC4H,OAAO;MAChBlI,CAAC,EAAEM,KAAK,CAAC6H;IACX,CAAC;IACD,IAAIJ,SAAS,CAAChD,OAAO,EAAE;MACrBgD,SAAS,CAAChD,OAAO,CAACqD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EACD,MAAMC,eAAe,GAAG,CAAC,CAAC;EAC1B,MAAMC,aAAa,GAAG,OAAOhF,KAAK,KAAK,QAAQ;EAC/C,IAAIpB,aAAa,EAAE;IACjBmG,eAAe,CAAC/E,KAAK,GAAG,CAAC3G,IAAI,IAAI2L,aAAa,IAAI,CAAClG,oBAAoB,GAAGkB,KAAK,GAAG,IAAI;IACtF+E,eAAe,CAAC,kBAAkB,CAAC,GAAG1L,IAAI,GAAGgG,EAAE,GAAG,IAAI;EACxD,CAAC,MAAM;IACL0F,eAAe,CAAC,YAAY,CAAC,GAAGC,aAAa,GAAGhF,KAAK,GAAG,IAAI;IAC5D+E,eAAe,CAAC,iBAAiB,CAAC,GAAG1L,IAAI,IAAI,CAAC2L,aAAa,GAAG3F,EAAE,GAAG,IAAI;EACzE;EACA,MAAMyE,aAAa,GAAG7N,QAAQ,CAAC,CAAC,CAAC,EAAE8O,eAAe,EAAE3E,KAAK,EAAE5B,QAAQ,CAACvF,KAAK,EAAE;IACzEgM,SAAS,EAAE5O,IAAI,CAAC+J,KAAK,CAAC6E,SAAS,EAAEzG,QAAQ,CAACvF,KAAK,CAACgM,SAAS,CAAC;IAC1DlB,YAAY,EAAEF,gBAAgB;IAC9BzG,GAAG,EAAEoH;EACP,CAAC,EAAEpF,YAAY,GAAG;IAChBuF,WAAW,EAAED;EACf,CAAC,GAAG,CAAC,CAAC,CAAC;EACP,IAAIpD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCsC,aAAa,CAAC,iCAAiC,CAAC,GAAG,IAAI;;IAEvD;IACA3N,KAAK,CAACyL,SAAS,CAAC,MAAM;MACpB,IAAItB,SAAS,IAAI,CAACA,SAAS,CAAC4E,YAAY,CAAC,iCAAiC,CAAC,EAAE;QAC3ElD,OAAO,CAACC,KAAK,CAAC,CAAC,qFAAqF,EAAE,wFAAwF,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC7M;IACF,CAAC,EAAE,CAAC5B,SAAS,CAAC,CAAC;EACjB;EACA,MAAM6E,2BAA2B,GAAG,CAAC,CAAC;EACtC,IAAI,CAACnG,oBAAoB,EAAE;IACzB8E,aAAa,CAACC,YAAY,GAAGC,gBAAgB;IAC7CF,aAAa,CAACI,UAAU,GAAGD,cAAc;EAC3C;EACA,IAAI,CAACnF,oBAAoB,EAAE;IACzBgF,aAAa,CAACsB,WAAW,GAAGvI,mBAAmB,CAACkG,eAAe,EAAEe,aAAa,CAACsB,WAAW,CAAC;IAC3FtB,aAAa,CAACuB,YAAY,GAAGxI,mBAAmB,CAACqG,gBAAgB,EAAEY,aAAa,CAACuB,YAAY,CAAC;IAC9F,IAAI,CAAChN,kBAAkB,EAAE;MACvB8M,2BAA2B,CAACC,WAAW,GAAGrC,eAAe;MACzDoC,2BAA2B,CAACE,YAAY,GAAGnC,gBAAgB;IAC7D;EACF;EACA,IAAI,CAACrE,oBAAoB,EAAE;IACzBiF,aAAa,CAACR,OAAO,GAAGzG,mBAAmB,CAAC8G,WAAW,EAAEG,aAAa,CAACR,OAAO,CAAC;IAC/EQ,aAAa,CAACV,MAAM,GAAGvG,mBAAmB,CAAC6G,UAAU,EAAEI,aAAa,CAACV,MAAM,CAAC;IAC5E,IAAI,CAAC/K,kBAAkB,EAAE;MACvB8M,2BAA2B,CAAC7B,OAAO,GAAGK,WAAW;MACjDwB,2BAA2B,CAAC/B,MAAM,GAAGM,UAAU;IACjD;EACF;EACA,IAAIpC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIhD,QAAQ,CAACvF,KAAK,CAAC+G,KAAK,EAAE;MACxBgC,OAAO,CAACC,KAAK,CAAC,CAAC,oEAAoE,EAAE,4BAA4BzD,QAAQ,CAACvF,KAAK,CAAC+G,KAAK,8BAA8B,CAAC,CAACkC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClL;EACF;EACA,MAAMoD,aAAa,GAAGnP,KAAK,CAACoP,OAAO,CAAC,MAAM;IACxC,IAAIC,qBAAqB;IACzB,IAAIC,gBAAgB,GAAG,CAAC;MACtB3M,IAAI,EAAE,OAAO;MACb4M,OAAO,EAAEC,OAAO,CAAClF,QAAQ,CAAC;MAC1BmF,OAAO,EAAE;QACPC,OAAO,EAAEpF,QAAQ;QACjBpF,OAAO,EAAE;MACX;IACF,CAAC,CAAC;IACF,IAAI,CAACmK,qBAAqB,GAAG1F,WAAW,CAACwF,aAAa,KAAK,IAAI,IAAIE,qBAAqB,CAACM,SAAS,EAAE;MAClGL,gBAAgB,GAAGA,gBAAgB,CAACM,MAAM,CAACjG,WAAW,CAACwF,aAAa,CAACQ,SAAS,CAAC;IACjF;IACA,OAAO7P,QAAQ,CAAC,CAAC,CAAC,EAAE6J,WAAW,CAACwF,aAAa,EAAE;MAC7CQ,SAAS,EAAEL;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChF,QAAQ,EAAEX,WAAW,CAAC,CAAC;EAC3B,MAAM3H,UAAU,GAAGlC,QAAQ,CAAC,CAAC,CAAC,EAAEgD,KAAK,EAAE;IACrCe,KAAK;IACL1B,KAAK;IACLD,kBAAkB;IAClBG,SAAS;IACTqH,mBAAmB;IACnBtH,KAAK,EAAEoI,oBAAoB,CAACc;EAC9B,CAAC,CAAC;EACF,MAAMrJ,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMyH,eAAe,GAAG,CAACvC,IAAI,GAAG,CAACC,aAAa,GAAG7E,KAAK,CAACC,MAAM,KAAK,IAAI,GAAG4E,aAAa,GAAGoB,UAAU,CAACvH,MAAM,KAAK,IAAI,GAAGkG,IAAI,GAAGxE,aAAa;EAC1I,MAAMoH,mBAAmB,GAAG,CAAC1C,KAAK,GAAG,CAACC,KAAK,GAAG,CAACC,iBAAiB,GAAGhF,KAAK,CAACuN,UAAU,KAAK,IAAI,GAAGvI,iBAAiB,GAAGiB,UAAU,CAACuH,UAAU,KAAK,IAAI,GAAGzI,KAAK,GAAG0C,uBAAuB,KAAK,IAAI,GAAG3C,KAAK,GAAGrG,IAAI;EAC3M,MAAMgP,gBAAgB,GAAG,CAACxI,KAAK,GAAG,CAACC,cAAc,GAAGlF,KAAK,CAACE,OAAO,KAAK,IAAI,GAAGgF,cAAc,GAAGe,UAAU,CAAC/D,OAAO,KAAK,IAAI,GAAG+C,KAAK,GAAGnD,cAAc;EAClJ,MAAM4L,cAAc,GAAG,CAACvI,KAAK,GAAG,CAACC,YAAY,GAAGpF,KAAK,CAACH,KAAK,KAAK,IAAI,GAAGuF,YAAY,GAAGa,UAAU,CAAC0H,KAAK,KAAK,IAAI,GAAGxI,KAAK,GAAG5B,YAAY;EACtI,MAAMqK,WAAW,GAAGzP,gBAAgB,CAACgJ,eAAe,EAAE3J,QAAQ,CAAC,CAAC,CAAC,EAAE6J,WAAW,EAAE,CAAChC,iBAAiB,GAAGiC,SAAS,CAACrH,MAAM,KAAK,IAAI,GAAGoF,iBAAiB,GAAGa,eAAe,CAACjG,MAAM,EAAE;IAC3KuM,SAAS,EAAE5O,IAAI,CAAC+B,OAAO,CAACM,MAAM,EAAEoH,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACmF,SAAS,EAAE,CAAClH,KAAK,GAAG,CAACC,kBAAkB,GAAG+B,SAAS,CAACrH,MAAM,KAAK,IAAI,GAAGsF,kBAAkB,GAAGW,eAAe,CAACjG,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGqF,KAAK,CAACkH,SAAS;EAClO,CAAC,CAAC,EAAE9M,UAAU,CAAC;EACf,MAAMmO,eAAe,GAAG1P,gBAAgB,CAACqJ,mBAAmB,EAAEhK,QAAQ,CAAC,CAAC,CAAC,EAAEkK,eAAe,EAAE,CAAClC,qBAAqB,GAAG8B,SAAS,CAACiG,UAAU,KAAK,IAAI,GAAG/H,qBAAqB,GAAGU,eAAe,CAACqH,UAAU,CAAC,EAAE7N,UAAU,CAAC;EACrN,MAAMoO,YAAY,GAAG3P,gBAAgB,CAACsP,gBAAgB,EAAEjQ,QAAQ,CAAC,CAAC,CAAC,EAAE,CAACiI,kBAAkB,GAAG6B,SAAS,CAACpH,OAAO,KAAK,IAAI,GAAGuF,kBAAkB,GAAGS,eAAe,CAAChG,OAAO,EAAE;IACpKsM,SAAS,EAAE5O,IAAI,CAAC+B,OAAO,CAACO,OAAO,EAAE,CAACwF,KAAK,GAAG,CAACC,mBAAmB,GAAG2B,SAAS,CAACpH,OAAO,KAAK,IAAI,GAAGyF,mBAAmB,GAAGO,eAAe,CAAChG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGwF,KAAK,CAAC8G,SAAS;EACjL,CAAC,CAAC,EAAE9M,UAAU,CAAC;EACf,MAAMqO,iBAAiB,GAAG5P,gBAAgB,CAACuP,cAAc,EAAElQ,QAAQ,CAAC,CAAC,CAAC,EAAE,CAACoI,gBAAgB,GAAG0B,SAAS,CAACzH,KAAK,KAAK,IAAI,GAAG+F,gBAAgB,GAAGM,eAAe,CAACrG,KAAK,EAAE;IAC/J2M,SAAS,EAAE5O,IAAI,CAAC+B,OAAO,CAACE,KAAK,EAAE,CAACgG,KAAK,GAAG,CAACC,iBAAiB,GAAGwB,SAAS,CAACzH,KAAK,KAAK,IAAI,GAAGiG,iBAAiB,GAAGI,eAAe,CAACrG,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgG,KAAK,CAAC2G,SAAS;EACvK,CAAC,CAAC,EAAE9M,UAAU,CAAC;EACf,OAAO,aAAaL,KAAK,CAAC3B,KAAK,CAACsQ,QAAQ,EAAE;IACxCjI,QAAQ,EAAE,CAAC,aAAarI,KAAK,CAACuQ,YAAY,CAAClI,QAAQ,EAAEsF,aAAa,CAAC,EAAE,aAAalM,IAAI,CAACgI,eAAe,EAAE3J,QAAQ,CAAC;MAC/G0Q,EAAE,EAAE9G,mBAAmB,IAAI,IAAI,GAAGA,mBAAmB,GAAG1I,MAAM;MAC9DqB,SAAS,EAAEA,SAAS;MACpBoO,QAAQ,EAAExH,YAAY,GAAG;QACvByH,qBAAqB,EAAEA,CAAA,MAAO;UAC5BlN,GAAG,EAAE6C,cAAc,CAACE,CAAC;UACrBzC,IAAI,EAAEuC,cAAc,CAACC,CAAC;UACtBtC,KAAK,EAAEqC,cAAc,CAACC,CAAC;UACvB3C,MAAM,EAAE0C,cAAc,CAACE,CAAC;UACxBpC,KAAK,EAAE,CAAC;UACRD,MAAM,EAAE;QACV,CAAC;MACH,CAAC,GAAGiG,SAAS;MACbmE,SAAS,EAAEA,SAAS;MACpBpL,IAAI,EAAEiH,SAAS,GAAGjH,IAAI,GAAG,KAAK;MAC9BgG,EAAE,EAAEA,EAAE;MACN2G,UAAU,EAAE;IACd,CAAC,EAAEb,2BAA2B,EAAEkB,WAAW,EAAE;MAC3Cf,aAAa,EAAEA,aAAa;MAC5B9G,QAAQ,EAAEA,CAAC;QACT2B,eAAe,EAAE2G;MACnB,CAAC,KAAK,aAAalP,IAAI,CAACqI,mBAAmB,EAAEhK,QAAQ,CAAC;QACpD8Q,OAAO,EAAExN,KAAK,CAACqJ,WAAW,CAACC,QAAQ,CAACmE;MACtC,CAAC,EAAEF,oBAAoB,EAAER,eAAe,EAAE;QACxC9H,QAAQ,EAAE,aAAa1G,KAAK,CAACoO,gBAAgB,EAAEjQ,QAAQ,CAAC,CAAC,CAAC,EAAEsQ,YAAY,EAAE;UACxE/H,QAAQ,EAAE,CAACwB,KAAK,EAAE1H,KAAK,GAAG,aAAaV,IAAI,CAACuO,cAAc,EAAElQ,QAAQ,CAAC,CAAC,CAAC,EAAEuQ,iBAAiB,EAAE;YAC1FpJ,GAAG,EAAEsD;UACP,CAAC,CAAC,CAAC,GAAG,IAAI;QACZ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7G,OAAO,CAACsM,SAAS,CAAC,yBAAyB;EACjF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACE3O,KAAK,EAAElC,SAAS,CAAC8Q,IAAI;EACrB;AACF;AACA;EACE1I,QAAQ,EAAEhI,mBAAmB,CAAC2Q,UAAU;EACxC;AACF;AACA;EACE/O,OAAO,EAAEhC,SAAS,CAACgR,MAAM;EACzB;AACF;AACA;EACEnC,SAAS,EAAE7O,SAAS,CAACiR,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE3I,UAAU,EAAEtI,SAAS,CAAC2E,KAAK,CAAC;IAC1BqL,KAAK,EAAEhQ,SAAS,CAACkR,WAAW;IAC5BnQ,MAAM,EAAEf,SAAS,CAACkR,WAAW;IAC7B3M,OAAO,EAAEvE,SAAS,CAACkR,WAAW;IAC9BrB,UAAU,EAAE7P,SAAS,CAACkR;EACxB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE3I,eAAe,EAAEvI,SAAS,CAAC2E,KAAK,CAAC;IAC/BzC,KAAK,EAAElC,SAAS,CAACgR,MAAM;IACvB1O,MAAM,EAAEtC,SAAS,CAACgR,MAAM;IACxBzO,OAAO,EAAEvC,SAAS,CAACgR,MAAM;IACzBpB,UAAU,EAAE5P,SAAS,CAACgR;EACxB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACExI,aAAa,EAAExI,SAAS,CAAC8Q,IAAI;EAC7B;AACF;AACA;AACA;EACErI,oBAAoB,EAAEzI,SAAS,CAAC8Q,IAAI;EACpC;AACF;AACA;AACA;EACEpI,oBAAoB,EAAE1I,SAAS,CAAC8Q,IAAI;EACpC;AACF;AACA;AACA;AACA;EACE7O,kBAAkB,EAAEjC,SAAS,CAAC8Q,IAAI;EAClC;AACF;AACA;AACA;EACElI,oBAAoB,EAAE5I,SAAS,CAAC8Q,IAAI;EACpC;AACF;AACA;AACA;AACA;EACEjI,UAAU,EAAE7I,SAAS,CAACmR,MAAM;EAC5B;AACF;AACA;AACA;EACErI,cAAc,EAAE9I,SAAS,CAACmR,MAAM;EAChC;AACF;AACA;AACA;EACEpI,eAAe,EAAE/I,SAAS,CAACmR,MAAM;EACjC;AACF;AACA;AACA;EACEnI,YAAY,EAAEhJ,SAAS,CAAC8Q,IAAI;EAC5B;AACF;AACA;AACA;EACE7H,EAAE,EAAEjJ,SAAS,CAACiR,MAAM;EACpB;AACF;AACA;AACA;AACA;EACE9H,UAAU,EAAEnJ,SAAS,CAACmR,MAAM;EAC5B;AACF;AACA;AACA;EACE/H,eAAe,EAAEpJ,SAAS,CAACmR,MAAM;EACjC;AACF;AACA;AACA;AACA;EACE9H,OAAO,EAAErJ,SAAS,CAACoR,IAAI;EACvB;AACF;AACA;AACA;AACA;EACE9H,MAAM,EAAEtJ,SAAS,CAACoR,IAAI;EACtB;AACF;AACA;EACEnO,IAAI,EAAEjD,SAAS,CAAC8Q,IAAI;EACpB;AACF;AACA;AACA;EACE1O,SAAS,EAAEpC,SAAS,CAACqR,KAAK,CAAC,CAAC,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;EAC1K;AACF;AACA;AACA;EACE7H,eAAe,EAAExJ,SAAS,CAACkR,WAAW;EACtC;AACF;AACA;AACA;EACExH,WAAW,EAAE1J,SAAS,CAACgR,MAAM;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACErH,SAAS,EAAE3J,SAAS,CAAC2E,KAAK,CAAC;IACzBzC,KAAK,EAAElC,SAAS,CAACgR,MAAM;IACvB1O,MAAM,EAAEtC,SAAS,CAACgR,MAAM;IACxBzO,OAAO,EAAEvC,SAAS,CAACgR,MAAM;IACzBpB,UAAU,EAAE5P,SAAS,CAACgR;EACxB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;EACE3O,KAAK,EAAErC,SAAS,CAAC2E,KAAK,CAAC;IACrBzC,KAAK,EAAElC,SAAS,CAACkR,WAAW;IAC5B5O,MAAM,EAAEtC,SAAS,CAACkR,WAAW;IAC7B3O,OAAO,EAAEvC,SAAS,CAACkR,WAAW;IAC9BtB,UAAU,EAAE5P,SAAS,CAACkR;EACxB,CAAC,CAAC;EACF;AACF;AACA;EACEI,EAAE,EAAEtR,SAAS,CAACuR,SAAS,CAAC,CAACvR,SAAS,CAACwR,OAAO,CAACxR,SAAS,CAACuR,SAAS,CAAC,CAACvR,SAAS,CAACoR,IAAI,EAAEpR,SAAS,CAACgR,MAAM,EAAEhR,SAAS,CAAC8Q,IAAI,CAAC,CAAC,CAAC,EAAE9Q,SAAS,CAACoR,IAAI,EAAEpR,SAAS,CAACgR,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACEpH,KAAK,EAAE5J,SAAS,CAACyR,IAAI;EACrB;AACF;AACA;AACA;AACA;EACE5H,mBAAmB,EAAE7J,SAAS,CAACkR,WAAW;EAC1C;AACF;AACA;AACA;EACEnH,eAAe,EAAE/J,SAAS,CAACgR;AAC7B,CAAC,GAAG,KAAK,CAAC;AACV,eAAezM,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}