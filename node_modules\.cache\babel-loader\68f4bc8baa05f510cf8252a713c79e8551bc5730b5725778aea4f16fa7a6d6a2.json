{"ast": null, "code": "module.exports = Function.call.bind(Object.prototype.hasOwnProperty);", "map": {"version": 3, "names": ["module", "exports", "Function", "call", "bind", "Object", "prototype", "hasOwnProperty"], "sources": ["D:/Safety Tracking Web App/node_modules/prop-types/lib/has.js"], "sourcesContent": ["module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,QAAQ,CAACC,IAAI,CAACC,IAAI,CAACC,MAAM,CAACC,SAAS,CAACC,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}