{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"icon\", \"label\", \"onChange\", \"onClick\", \"selected\", \"showLabel\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport ButtonBase from '../ButtonBase';\nimport unsupportedProp from '../utils/unsupportedProp';\nimport bottomNavigationActionClasses, { getBottomNavigationActionUtilityClass } from './bottomNavigationActionClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    showLabel,\n    selected\n  } = ownerState;\n  const slots = {\n    root: ['root', !showLabel && !selected && 'iconOnly', selected && 'selected'],\n    label: ['label', !showLabel && !selected && 'iconOnly', selected && 'selected']\n  };\n  return composeClasses(slots, getBottomNavigationActionUtilityClass, classes);\n};\nconst BottomNavigationActionRoot = styled(ButtonBase, {\n  name: 'MuiBottomNavigationAction',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.showLabel && !ownerState.selected && styles.iconOnly];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  transition: theme.transitions.create(['color', 'padding-top'], {\n    duration: theme.transitions.duration.short\n  }),\n  padding: '0px 12px',\n  minWidth: 80,\n  maxWidth: 168,\n  color: (theme.vars || theme).palette.text.secondary,\n  flexDirection: 'column',\n  flex: '1'\n}, !ownerState.showLabel && !ownerState.selected && {\n  paddingTop: 14\n}, !ownerState.showLabel && !ownerState.selected && !ownerState.label && {\n  paddingTop: 0\n}, {\n  [`&.${bottomNavigationActionClasses.selected}`]: {\n    color: (theme.vars || theme).palette.primary.main\n  }\n}));\nconst BottomNavigationActionLabel = styled('span', {\n  name: 'MuiBottomNavigationAction',\n  slot: 'Label',\n  overridesResolver: (props, styles) => styles.label\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  fontFamily: theme.typography.fontFamily,\n  fontSize: theme.typography.pxToRem(12),\n  opacity: 1,\n  transition: 'font-size 0.2s, opacity 0.2s',\n  transitionDelay: '0.1s'\n}, !ownerState.showLabel && !ownerState.selected && {\n  opacity: 0,\n  transitionDelay: '0s'\n}, {\n  [`&.${bottomNavigationActionClasses.selected}`]: {\n    fontSize: theme.typography.pxToRem(14)\n  }\n}));\nconst BottomNavigationAction = /*#__PURE__*/React.forwardRef(function BottomNavigationAction(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiBottomNavigationAction'\n  });\n  const {\n      className,\n      icon,\n      label,\n      onChange,\n      onClick\n      // eslint-disable-next-line react/prop-types -- private, always overridden by BottomNavigation\n      ,\n\n      value\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const handleChange = event => {\n    if (onChange) {\n      onChange(event, value);\n    }\n    if (onClick) {\n      onClick(event);\n    }\n  };\n  return /*#__PURE__*/_jsxs(BottomNavigationActionRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    focusRipple: true,\n    onClick: handleChange,\n    ownerState: ownerState\n  }, other, {\n    children: [icon, /*#__PURE__*/_jsx(BottomNavigationActionLabel, {\n      className: classes.label,\n      ownerState: ownerState,\n      children: label\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? BottomNavigationAction.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * This prop isn't supported.\n   * Use the `component` prop if you need to change the children structure.\n   */\n  children: unsupportedProp,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The icon to display.\n   */\n  icon: PropTypes.node,\n  /**\n   * The label element.\n   */\n  label: PropTypes.node,\n  /**\n   * @ignore\n   */\n  onChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * If `true`, the `BottomNavigationAction` will show its label.\n   * By default, only the selected `BottomNavigationAction`\n   * inside `BottomNavigation` will show its label.\n   *\n   * The prop defaults to the value (`false`) inherited from the parent BottomNavigation component.\n   */\n  showLabel: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * You can provide your own value. Otherwise, we fallback to the child position index.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default BottomNavigationAction;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "ButtonBase", "unsupportedProp", "bottomNavigationActionClasses", "getBottomNavigationActionUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "showLabel", "selected", "slots", "root", "label", "BottomNavigationActionRoot", "name", "slot", "overridesResolver", "props", "styles", "iconOnly", "theme", "transition", "transitions", "create", "duration", "short", "padding", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "color", "vars", "palette", "text", "secondary", "flexDirection", "flex", "paddingTop", "primary", "main", "BottomNavigationActionLabel", "fontFamily", "typography", "fontSize", "pxToRem", "opacity", "transitionDelay", "BottomNavigationAction", "forwardRef", "inProps", "ref", "className", "icon", "onChange", "onClick", "value", "other", "handleChange", "event", "focusRipple", "children", "process", "env", "NODE_ENV", "propTypes", "object", "string", "node", "func", "bool", "sx", "oneOfType", "arrayOf", "any"], "sources": ["D:/Safety Tracking Web App/node_modules/@mui/material/BottomNavigationAction/BottomNavigationAction.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"icon\", \"label\", \"onChange\", \"onClick\", \"selected\", \"showLabel\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport ButtonBase from '../ButtonBase';\nimport unsupportedProp from '../utils/unsupportedProp';\nimport bottomNavigationActionClasses, { getBottomNavigationActionUtilityClass } from './bottomNavigationActionClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    showLabel,\n    selected\n  } = ownerState;\n  const slots = {\n    root: ['root', !showLabel && !selected && 'iconOnly', selected && 'selected'],\n    label: ['label', !showLabel && !selected && 'iconOnly', selected && 'selected']\n  };\n  return composeClasses(slots, getBottomNavigationActionUtilityClass, classes);\n};\nconst BottomNavigationActionRoot = styled(ButtonBase, {\n  name: 'MuiBottomNavigationAction',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.showLabel && !ownerState.selected && styles.iconOnly];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  transition: theme.transitions.create(['color', 'padding-top'], {\n    duration: theme.transitions.duration.short\n  }),\n  padding: '0px 12px',\n  minWidth: 80,\n  maxWidth: 168,\n  color: (theme.vars || theme).palette.text.secondary,\n  flexDirection: 'column',\n  flex: '1'\n}, !ownerState.showLabel && !ownerState.selected && {\n  paddingTop: 14\n}, !ownerState.showLabel && !ownerState.selected && !ownerState.label && {\n  paddingTop: 0\n}, {\n  [`&.${bottomNavigationActionClasses.selected}`]: {\n    color: (theme.vars || theme).palette.primary.main\n  }\n}));\nconst BottomNavigationActionLabel = styled('span', {\n  name: 'MuiBottomNavigationAction',\n  slot: 'Label',\n  overridesResolver: (props, styles) => styles.label\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  fontFamily: theme.typography.fontFamily,\n  fontSize: theme.typography.pxToRem(12),\n  opacity: 1,\n  transition: 'font-size 0.2s, opacity 0.2s',\n  transitionDelay: '0.1s'\n}, !ownerState.showLabel && !ownerState.selected && {\n  opacity: 0,\n  transitionDelay: '0s'\n}, {\n  [`&.${bottomNavigationActionClasses.selected}`]: {\n    fontSize: theme.typography.pxToRem(14)\n  }\n}));\nconst BottomNavigationAction = /*#__PURE__*/React.forwardRef(function BottomNavigationAction(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiBottomNavigationAction'\n  });\n  const {\n      className,\n      icon,\n      label,\n      onChange,\n      onClick\n      // eslint-disable-next-line react/prop-types -- private, always overridden by BottomNavigation\n      ,\n\n      value\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const handleChange = event => {\n    if (onChange) {\n      onChange(event, value);\n    }\n    if (onClick) {\n      onClick(event);\n    }\n  };\n  return /*#__PURE__*/_jsxs(BottomNavigationActionRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    focusRipple: true,\n    onClick: handleChange,\n    ownerState: ownerState\n  }, other, {\n    children: [icon, /*#__PURE__*/_jsx(BottomNavigationActionLabel, {\n      className: classes.label,\n      ownerState: ownerState,\n      children: label\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? BottomNavigationAction.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * This prop isn't supported.\n   * Use the `component` prop if you need to change the children structure.\n   */\n  children: unsupportedProp,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The icon to display.\n   */\n  icon: PropTypes.node,\n  /**\n   * The label element.\n   */\n  label: PropTypes.node,\n  /**\n   * @ignore\n   */\n  onChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * If `true`, the `BottomNavigationAction` will show its label.\n   * By default, only the selected `BottomNavigationAction`\n   * inside `BottomNavigation` will show its label.\n   *\n   * The prop defaults to the value (`false`) inherited from the parent BottomNavigation component.\n   */\n  showLabel: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * You can provide your own value. Otherwise, we fallback to the child position index.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default BottomNavigationAction;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;AACzG,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,6BAA6B,IAAIC,qCAAqC,QAAQ,iCAAiC;AACtH,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,SAAS;IACTC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,CAACH,SAAS,IAAI,CAACC,QAAQ,IAAI,UAAU,EAAEA,QAAQ,IAAI,UAAU,CAAC;IAC7EG,KAAK,EAAE,CAAC,OAAO,EAAE,CAACJ,SAAS,IAAI,CAACC,QAAQ,IAAI,UAAU,EAAEA,QAAQ,IAAI,UAAU;EAChF,CAAC;EACD,OAAOf,cAAc,CAACgB,KAAK,EAAEV,qCAAqC,EAAEO,OAAO,CAAC;AAC9E,CAAC;AACD,MAAMM,0BAA0B,GAAGlB,MAAM,CAACE,UAAU,EAAE;EACpDiB,IAAI,EAAE,2BAA2B;EACjCC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJZ;IACF,CAAC,GAAGW,KAAK;IACT,OAAO,CAACC,MAAM,CAACP,IAAI,EAAE,CAACL,UAAU,CAACE,SAAS,IAAI,CAACF,UAAU,CAACG,QAAQ,IAAIS,MAAM,CAACC,QAAQ,CAAC;EACxF;AACF,CAAC,CAAC,CAAC,CAAC;EACFC,KAAK;EACLd;AACF,CAAC,KAAKjB,QAAQ,CAAC;EACbgC,UAAU,EAAED,KAAK,CAACE,WAAW,CAACC,MAAM,CAAC,CAAC,OAAO,EAAE,aAAa,CAAC,EAAE;IAC7DC,QAAQ,EAAEJ,KAAK,CAACE,WAAW,CAACE,QAAQ,CAACC;EACvC,CAAC,CAAC;EACFC,OAAO,EAAE,UAAU;EACnBC,QAAQ,EAAE,EAAE;EACZC,QAAQ,EAAE,GAAG;EACbC,KAAK,EAAE,CAACT,KAAK,CAACU,IAAI,IAAIV,KAAK,EAAEW,OAAO,CAACC,IAAI,CAACC,SAAS;EACnDC,aAAa,EAAE,QAAQ;EACvBC,IAAI,EAAE;AACR,CAAC,EAAE,CAAC7B,UAAU,CAACE,SAAS,IAAI,CAACF,UAAU,CAACG,QAAQ,IAAI;EAClD2B,UAAU,EAAE;AACd,CAAC,EAAE,CAAC9B,UAAU,CAACE,SAAS,IAAI,CAACF,UAAU,CAACG,QAAQ,IAAI,CAACH,UAAU,CAACM,KAAK,IAAI;EACvEwB,UAAU,EAAE;AACd,CAAC,EAAE;EACD,CAAC,KAAKrC,6BAA6B,CAACU,QAAQ,EAAE,GAAG;IAC/CoB,KAAK,EAAE,CAACT,KAAK,CAACU,IAAI,IAAIV,KAAK,EAAEW,OAAO,CAACM,OAAO,CAACC;EAC/C;AACF,CAAC,CAAC,CAAC;AACH,MAAMC,2BAA2B,GAAG5C,MAAM,CAAC,MAAM,EAAE;EACjDmB,IAAI,EAAE,2BAA2B;EACjCC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC,CAAC;EACFQ,KAAK;EACLd;AACF,CAAC,KAAKjB,QAAQ,CAAC;EACbmD,UAAU,EAAEpB,KAAK,CAACqB,UAAU,CAACD,UAAU;EACvCE,QAAQ,EAAEtB,KAAK,CAACqB,UAAU,CAACE,OAAO,CAAC,EAAE,CAAC;EACtCC,OAAO,EAAE,CAAC;EACVvB,UAAU,EAAE,8BAA8B;EAC1CwB,eAAe,EAAE;AACnB,CAAC,EAAE,CAACvC,UAAU,CAACE,SAAS,IAAI,CAACF,UAAU,CAACG,QAAQ,IAAI;EAClDmC,OAAO,EAAE,CAAC;EACVC,eAAe,EAAE;AACnB,CAAC,EAAE;EACD,CAAC,KAAK9C,6BAA6B,CAACU,QAAQ,EAAE,GAAG;IAC/CiC,QAAQ,EAAEtB,KAAK,CAACqB,UAAU,CAACE,OAAO,CAAC,EAAE;EACvC;AACF,CAAC,CAAC,CAAC;AACH,MAAMG,sBAAsB,GAAG,aAAavD,KAAK,CAACwD,UAAU,CAAC,SAASD,sBAAsBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzG,MAAMhC,KAAK,GAAGrB,eAAe,CAAC;IAC5BqB,KAAK,EAAE+B,OAAO;IACdlC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFoC,SAAS;MACTC,IAAI;MACJvC,KAAK;MACLwC,QAAQ;MACRC;MACA;MAAA;;MAGAC;IACF,CAAC,GAAGrC,KAAK;IACTsC,KAAK,GAAGnE,6BAA6B,CAAC6B,KAAK,EAAE3B,SAAS,CAAC;EACzD,MAAMgB,UAAU,GAAGW,KAAK;EACxB,MAAMV,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMkD,YAAY,GAAGC,KAAK,IAAI;IAC5B,IAAIL,QAAQ,EAAE;MACZA,QAAQ,CAACK,KAAK,EAAEH,KAAK,CAAC;IACxB;IACA,IAAID,OAAO,EAAE;MACXA,OAAO,CAACI,KAAK,CAAC;IAChB;EACF,CAAC;EACD,OAAO,aAAarD,KAAK,CAACS,0BAA0B,EAAExB,QAAQ,CAAC;IAC7D4D,GAAG,EAAEA,GAAG;IACRC,SAAS,EAAEzD,IAAI,CAACc,OAAO,CAACI,IAAI,EAAEuC,SAAS,CAAC;IACxCQ,WAAW,EAAE,IAAI;IACjBL,OAAO,EAAEG,YAAY;IACrBlD,UAAU,EAAEA;EACd,CAAC,EAAEiD,KAAK,EAAE;IACRI,QAAQ,EAAE,CAACR,IAAI,EAAE,aAAajD,IAAI,CAACqC,2BAA2B,EAAE;MAC9DW,SAAS,EAAE3C,OAAO,CAACK,KAAK;MACxBN,UAAU,EAAEA,UAAU;MACtBqD,QAAQ,EAAE/C;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFgD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGhB,sBAAsB,CAACiB,SAAS,CAAC,yBAAyB;EAChG;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEJ,QAAQ,EAAE7D,eAAe;EACzB;AACF;AACA;EACES,OAAO,EAAEf,SAAS,CAACwE,MAAM;EACzB;AACF;AACA;EACEd,SAAS,EAAE1D,SAAS,CAACyE,MAAM;EAC3B;AACF;AACA;EACEd,IAAI,EAAE3D,SAAS,CAAC0E,IAAI;EACpB;AACF;AACA;EACEtD,KAAK,EAAEpB,SAAS,CAAC0E,IAAI;EACrB;AACF;AACA;EACEd,QAAQ,EAAE5D,SAAS,CAAC2E,IAAI;EACxB;AACF;AACA;EACEd,OAAO,EAAE7D,SAAS,CAAC2E,IAAI;EACvB;AACF;AACA;AACA;AACA;AACA;AACA;EACE3D,SAAS,EAAEhB,SAAS,CAAC4E,IAAI;EACzB;AACF;AACA;EACEC,EAAE,EAAE7E,SAAS,CAAC8E,SAAS,CAAC,CAAC9E,SAAS,CAAC+E,OAAO,CAAC/E,SAAS,CAAC8E,SAAS,CAAC,CAAC9E,SAAS,CAAC2E,IAAI,EAAE3E,SAAS,CAACwE,MAAM,EAAExE,SAAS,CAAC4E,IAAI,CAAC,CAAC,CAAC,EAAE5E,SAAS,CAAC2E,IAAI,EAAE3E,SAAS,CAACwE,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACEV,KAAK,EAAE9D,SAAS,CAACgF;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAe1B,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}