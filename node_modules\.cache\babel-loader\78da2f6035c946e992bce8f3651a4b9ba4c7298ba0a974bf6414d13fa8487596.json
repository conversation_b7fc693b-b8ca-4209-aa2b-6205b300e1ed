{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"avatar\", \"className\", \"clickable\", \"color\", \"component\", \"deleteIcon\", \"disabled\", \"icon\", \"label\", \"onClick\", \"onDelete\", \"onKeyDown\", \"onKeyUp\", \"size\", \"variant\", \"tabIndex\", \"skipFocusWhenDisabled\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport CancelIcon from '../internal/svg-icons/Cancel';\nimport useForkRef from '../utils/useForkRef';\nimport unsupportedProp from '../utils/unsupportedProp';\nimport capitalize from '../utils/capitalize';\nimport ButtonBase from '../ButtonBase';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport chipClasses, { getChipUtilityClass } from './chipClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disabled,\n    size,\n    color,\n    iconColor,\n    onDelete,\n    clickable,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, disabled && 'disabled', `size${capitalize(size)}`, `color${capitalize(color)}`, clickable && 'clickable', clickable && `clickableColor${capitalize(color)}`, onDelete && 'deletable', onDelete && `deletableColor${capitalize(color)}`, `${variant}${capitalize(color)}`],\n    label: ['label', `label${capitalize(size)}`],\n    avatar: ['avatar', `avatar${capitalize(size)}`, `avatarColor${capitalize(color)}`],\n    icon: ['icon', `icon${capitalize(size)}`, `iconColor${capitalize(iconColor)}`],\n    deleteIcon: ['deleteIcon', `deleteIcon${capitalize(size)}`, `deleteIconColor${capitalize(color)}`, `deleteIcon${capitalize(variant)}Color${capitalize(color)}`]\n  };\n  return composeClasses(slots, getChipUtilityClass, classes);\n};\nconst ChipRoot = styled('div', {\n  name: 'MuiChip',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      color,\n      iconColor,\n      clickable,\n      onDelete,\n      size,\n      variant\n    } = ownerState;\n    return [{\n      [`& .${chipClasses.avatar}`]: styles.avatar\n    }, {\n      [`& .${chipClasses.avatar}`]: styles[`avatar${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.avatar}`]: styles[`avatarColor${capitalize(color)}`]\n    }, {\n      [`& .${chipClasses.icon}`]: styles.icon\n    }, {\n      [`& .${chipClasses.icon}`]: styles[`icon${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.icon}`]: styles[`iconColor${capitalize(iconColor)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles.deleteIcon\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIcon${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIconColor${capitalize(color)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIcon${capitalize(variant)}Color${capitalize(color)}`]\n    }, styles.root, styles[`size${capitalize(size)}`], styles[`color${capitalize(color)}`], clickable && styles.clickable, clickable && color !== 'default' && styles[`clickableColor${capitalize(color)})`], onDelete && styles.deletable, onDelete && color !== 'default' && styles[`deletableColor${capitalize(color)}`], styles[variant], styles[`${variant}${capitalize(color)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  const textColor = theme.palette.mode === 'light' ? theme.palette.grey[700] : theme.palette.grey[300];\n  return _extends({\n    maxWidth: '100%',\n    fontFamily: theme.typography.fontFamily,\n    fontSize: theme.typography.pxToRem(13),\n    display: 'inline-flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    height: 32,\n    color: (theme.vars || theme).palette.text.primary,\n    backgroundColor: (theme.vars || theme).palette.action.selected,\n    borderRadius: 32 / 2,\n    whiteSpace: 'nowrap',\n    transition: theme.transitions.create(['background-color', 'box-shadow']),\n    // reset cursor explicitly in case ButtonBase is used\n    cursor: 'unset',\n    // We disable the focus ring for mouse, touch and keyboard users.\n    outline: 0,\n    textDecoration: 'none',\n    border: 0,\n    // Remove `button` border\n    padding: 0,\n    // Remove `button` padding\n    verticalAlign: 'middle',\n    boxSizing: 'border-box',\n    [`&.${chipClasses.disabled}`]: {\n      opacity: (theme.vars || theme).palette.action.disabledOpacity,\n      pointerEvents: 'none'\n    },\n    [`& .${chipClasses.avatar}`]: {\n      marginLeft: 5,\n      marginRight: -6,\n      width: 24,\n      height: 24,\n      color: theme.vars ? theme.vars.palette.Chip.defaultAvatarColor : textColor,\n      fontSize: theme.typography.pxToRem(12)\n    },\n    [`& .${chipClasses.avatarColorPrimary}`]: {\n      color: (theme.vars || theme).palette.primary.contrastText,\n      backgroundColor: (theme.vars || theme).palette.primary.dark\n    },\n    [`& .${chipClasses.avatarColorSecondary}`]: {\n      color: (theme.vars || theme).palette.secondary.contrastText,\n      backgroundColor: (theme.vars || theme).palette.secondary.dark\n    },\n    [`& .${chipClasses.avatarSmall}`]: {\n      marginLeft: 4,\n      marginRight: -4,\n      width: 18,\n      height: 18,\n      fontSize: theme.typography.pxToRem(10)\n    },\n    [`& .${chipClasses.icon}`]: _extends({\n      marginLeft: 5,\n      marginRight: -6\n    }, ownerState.size === 'small' && {\n      fontSize: 18,\n      marginLeft: 4,\n      marginRight: -4\n    }, ownerState.iconColor === ownerState.color && _extends({\n      color: theme.vars ? theme.vars.palette.Chip.defaultIconColor : textColor\n    }, ownerState.color !== 'default' && {\n      color: 'inherit'\n    })),\n    [`& .${chipClasses.deleteIcon}`]: _extends({\n      WebkitTapHighlightColor: 'transparent',\n      color: theme.vars ? `rgba(${theme.vars.palette.text.primaryChannel} / 0.26)` : alpha(theme.palette.text.primary, 0.26),\n      fontSize: 22,\n      cursor: 'pointer',\n      margin: '0 5px 0 -6px',\n      '&:hover': {\n        color: theme.vars ? `rgba(${theme.vars.palette.text.primaryChannel} / 0.4)` : alpha(theme.palette.text.primary, 0.4)\n      }\n    }, ownerState.size === 'small' && {\n      fontSize: 16,\n      marginRight: 4,\n      marginLeft: -4\n    }, ownerState.color !== 'default' && {\n      color: theme.vars ? `rgba(${theme.vars.palette[ownerState.color].contrastTextChannel} / 0.7)` : alpha(theme.palette[ownerState.color].contrastText, 0.7),\n      '&:hover, &:active': {\n        color: (theme.vars || theme).palette[ownerState.color].contrastText\n      }\n    })\n  }, ownerState.size === 'small' && {\n    height: 24\n  }, ownerState.color !== 'default' && {\n    backgroundColor: (theme.vars || theme).palette[ownerState.color].main,\n    color: (theme.vars || theme).palette[ownerState.color].contrastText\n  }, ownerState.onDelete && {\n    [`&.${chipClasses.focusVisible}`]: {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n    }\n  }, ownerState.onDelete && ownerState.color !== 'default' && {\n    [`&.${chipClasses.focusVisible}`]: {\n      backgroundColor: (theme.vars || theme).palette[ownerState.color].dark\n    }\n  });\n}, ({\n  theme,\n  ownerState\n}) => _extends({}, ownerState.clickable && {\n  userSelect: 'none',\n  WebkitTapHighlightColor: 'transparent',\n  cursor: 'pointer',\n  '&:hover': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity)\n  },\n  [`&.${chipClasses.focusVisible}`]: {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n  },\n  '&:active': {\n    boxShadow: (theme.vars || theme).shadows[1]\n  }\n}, ownerState.clickable && ownerState.color !== 'default' && {\n  [`&:hover, &.${chipClasses.focusVisible}`]: {\n    backgroundColor: (theme.vars || theme).palette[ownerState.color].dark\n  }\n}), ({\n  theme,\n  ownerState\n}) => _extends({}, ownerState.variant === 'outlined' && {\n  backgroundColor: 'transparent',\n  border: theme.vars ? `1px solid ${theme.vars.palette.Chip.defaultBorder}` : `1px solid ${theme.palette.mode === 'light' ? theme.palette.grey[400] : theme.palette.grey[700]}`,\n  [`&.${chipClasses.clickable}:hover`]: {\n    backgroundColor: (theme.vars || theme).palette.action.hover\n  },\n  [`&.${chipClasses.focusVisible}`]: {\n    backgroundColor: (theme.vars || theme).palette.action.focus\n  },\n  [`& .${chipClasses.avatar}`]: {\n    marginLeft: 4\n  },\n  [`& .${chipClasses.avatarSmall}`]: {\n    marginLeft: 2\n  },\n  [`& .${chipClasses.icon}`]: {\n    marginLeft: 4\n  },\n  [`& .${chipClasses.iconSmall}`]: {\n    marginLeft: 2\n  },\n  [`& .${chipClasses.deleteIcon}`]: {\n    marginRight: 5\n  },\n  [`& .${chipClasses.deleteIconSmall}`]: {\n    marginRight: 3\n  }\n}, ownerState.variant === 'outlined' && ownerState.color !== 'default' && {\n  color: (theme.vars || theme).palette[ownerState.color].main,\n  border: `1px solid ${theme.vars ? `rgba(${theme.vars.palette[ownerState.color].mainChannel} / 0.7)` : alpha(theme.palette[ownerState.color].main, 0.7)}`,\n  [`&.${chipClasses.clickable}:hover`]: {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette[ownerState.color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[ownerState.color].main, theme.palette.action.hoverOpacity)\n  },\n  [`&.${chipClasses.focusVisible}`]: {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette[ownerState.color].mainChannel} / ${theme.vars.palette.action.focusOpacity})` : alpha(theme.palette[ownerState.color].main, theme.palette.action.focusOpacity)\n  },\n  [`& .${chipClasses.deleteIcon}`]: {\n    color: theme.vars ? `rgba(${theme.vars.palette[ownerState.color].mainChannel} / 0.7)` : alpha(theme.palette[ownerState.color].main, 0.7),\n    '&:hover, &:active': {\n      color: (theme.vars || theme).palette[ownerState.color].main\n    }\n  }\n}));\nconst ChipLabel = styled('span', {\n  name: 'MuiChip',\n  slot: 'Label',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      size\n    } = ownerState;\n    return [styles.label, styles[`label${capitalize(size)}`]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  overflow: 'hidden',\n  textOverflow: 'ellipsis',\n  paddingLeft: 12,\n  paddingRight: 12,\n  whiteSpace: 'nowrap'\n}, ownerState.variant === 'outlined' && {\n  paddingLeft: 11,\n  paddingRight: 11\n}, ownerState.size === 'small' && {\n  paddingLeft: 8,\n  paddingRight: 8\n}, ownerState.size === 'small' && ownerState.variant === 'outlined' && {\n  paddingLeft: 7,\n  paddingRight: 7\n}));\nfunction isDeleteKeyboardEvent(keyboardEvent) {\n  return keyboardEvent.key === 'Backspace' || keyboardEvent.key === 'Delete';\n}\n\n/**\n * Chips represent complex entities in small blocks, such as a contact.\n */\nconst Chip = /*#__PURE__*/React.forwardRef(function Chip(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiChip'\n  });\n  const {\n      avatar: avatarProp,\n      className,\n      clickable: clickableProp,\n      color = 'default',\n      component: ComponentProp,\n      deleteIcon: deleteIconProp,\n      disabled = false,\n      icon: iconProp,\n      label,\n      onClick,\n      onDelete,\n      onKeyDown,\n      onKeyUp,\n      size = 'medium',\n      variant = 'filled',\n      tabIndex,\n      skipFocusWhenDisabled = false // TODO v6: Rename to `focusableWhenDisabled`.\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const chipRef = React.useRef(null);\n  const handleRef = useForkRef(chipRef, ref);\n  const handleDeleteIconClick = event => {\n    // Stop the event from bubbling up to the `Chip`\n    event.stopPropagation();\n    if (onDelete) {\n      onDelete(event);\n    }\n  };\n  const handleKeyDown = event => {\n    // Ignore events from children of `Chip`.\n    if (event.currentTarget === event.target && isDeleteKeyboardEvent(event)) {\n      // Will be handled in keyUp, otherwise some browsers\n      // might init navigation\n      event.preventDefault();\n    }\n    if (onKeyDown) {\n      onKeyDown(event);\n    }\n  };\n  const handleKeyUp = event => {\n    // Ignore events from children of `Chip`.\n    if (event.currentTarget === event.target) {\n      if (onDelete && isDeleteKeyboardEvent(event)) {\n        onDelete(event);\n      } else if (event.key === 'Escape' && chipRef.current) {\n        chipRef.current.blur();\n      }\n    }\n    if (onKeyUp) {\n      onKeyUp(event);\n    }\n  };\n  const clickable = clickableProp !== false && onClick ? true : clickableProp;\n  const component = clickable || onDelete ? ButtonBase : ComponentProp || 'div';\n  const ownerState = _extends({}, props, {\n    component,\n    disabled,\n    size,\n    color,\n    iconColor: /*#__PURE__*/React.isValidElement(iconProp) ? iconProp.props.color || color : color,\n    onDelete: !!onDelete,\n    clickable,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const moreProps = component === ButtonBase ? _extends({\n    component: ComponentProp || 'div',\n    focusVisibleClassName: classes.focusVisible\n  }, onDelete && {\n    disableRipple: true\n  }) : {};\n  let deleteIcon = null;\n  if (onDelete) {\n    deleteIcon = deleteIconProp && /*#__PURE__*/React.isValidElement(deleteIconProp) ? (/*#__PURE__*/React.cloneElement(deleteIconProp, {\n      className: clsx(deleteIconProp.props.className, classes.deleteIcon),\n      onClick: handleDeleteIconClick\n    })) : /*#__PURE__*/_jsx(CancelIcon, {\n      className: clsx(classes.deleteIcon),\n      onClick: handleDeleteIconClick\n    });\n  }\n  let avatar = null;\n  if (avatarProp && /*#__PURE__*/React.isValidElement(avatarProp)) {\n    avatar = /*#__PURE__*/React.cloneElement(avatarProp, {\n      className: clsx(classes.avatar, avatarProp.props.className)\n    });\n  }\n  let icon = null;\n  if (iconProp && /*#__PURE__*/React.isValidElement(iconProp)) {\n    icon = /*#__PURE__*/React.cloneElement(iconProp, {\n      className: clsx(classes.icon, iconProp.props.className)\n    });\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (avatar && icon) {\n      console.error('MUI: The Chip component can not handle the avatar ' + 'and the icon prop at the same time. Pick one.');\n    }\n  }\n  return /*#__PURE__*/_jsxs(ChipRoot, _extends({\n    as: component,\n    className: clsx(classes.root, className),\n    disabled: clickable && disabled ? true : undefined,\n    onClick: onClick,\n    onKeyDown: handleKeyDown,\n    onKeyUp: handleKeyUp,\n    ref: handleRef,\n    tabIndex: skipFocusWhenDisabled && disabled ? -1 : tabIndex,\n    ownerState: ownerState\n  }, moreProps, other, {\n    children: [avatar || icon, /*#__PURE__*/_jsx(ChipLabel, {\n      className: clsx(classes.label),\n      ownerState: ownerState,\n      children: label\n    }), deleteIcon]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Chip.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The Avatar element to display.\n   */\n  avatar: PropTypes.element,\n  /**\n   * This prop isn't supported.\n   * Use the `component` prop if you need to change the children structure.\n   */\n  children: unsupportedProp,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the chip will appear clickable, and will raise when pressed,\n   * even if the onClick prop is not defined.\n   * If `false`, the chip will not appear clickable, even if onClick prop is defined.\n   * This can be used, for example,\n   * along with the component prop to indicate an anchor Chip is clickable.\n   * Note: this controls the UI and does not affect the onClick event.\n   */\n  clickable: PropTypes.bool,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'default'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Override the default delete icon element. Shown only if `onDelete` is set.\n   */\n  deleteIcon: PropTypes.element,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Icon element.\n   */\n  icon: PropTypes.element,\n  /**\n   * The content of the component.\n   */\n  label: PropTypes.node,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * Callback fired when the delete icon is clicked.\n   * If set, the delete icon will be shown.\n   */\n  onDelete: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyUp: PropTypes.func,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * If `true`, allows the disabled chip to escape focus.\n   * If `false`, allows the disabled chip to receive focus.\n   * @default false\n   */\n  skipFocusWhenDisabled: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * @ignore\n   */\n  tabIndex: PropTypes.number,\n  /**\n   * The variant to use.\n   * @default 'filled'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['filled', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Chip;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "alpha", "CancelIcon", "useForkRef", "unsupportedProp", "capitalize", "ButtonBase", "useDefaultProps", "styled", "chipClasses", "getChipUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "disabled", "size", "color", "iconColor", "onDelete", "clickable", "variant", "slots", "root", "label", "avatar", "icon", "deleteIcon", "ChipRoot", "name", "slot", "overridesResolver", "props", "styles", "deletable", "theme", "textColor", "palette", "mode", "grey", "max<PERSON><PERSON><PERSON>", "fontFamily", "typography", "fontSize", "pxToRem", "display", "alignItems", "justifyContent", "height", "vars", "text", "primary", "backgroundColor", "action", "selected", "borderRadius", "whiteSpace", "transition", "transitions", "create", "cursor", "outline", "textDecoration", "border", "padding", "verticalAlign", "boxSizing", "opacity", "disabledOpacity", "pointerEvents", "marginLeft", "marginRight", "width", "Chip", "defaultAvatarColor", "avatarColorPrimary", "contrastText", "dark", "avatarColorSecondary", "secondary", "avatar<PERSON><PERSON><PERSON>", "defaultIconColor", "WebkitTapHighlightColor", "primaryChannel", "margin", "contrastTextChannel", "main", "focusVisible", "selectedChannel", "selectedOpacity", "focusOpacity", "userSelect", "hoverOpacity", "boxShadow", "shadows", "defaultBorder", "hover", "focus", "iconSmall", "deleteIconSmall", "mainChannel", "ChipLabel", "overflow", "textOverflow", "paddingLeft", "paddingRight", "isDeleteKeyboardEvent", "keyboardEvent", "key", "forwardRef", "inProps", "ref", "avatarProp", "className", "clickableProp", "component", "ComponentProp", "deleteIconProp", "iconProp", "onClick", "onKeyDown", "onKeyUp", "tabIndex", "skipFocusWhenDisabled", "other", "chipRef", "useRef", "handleRef", "handleDeleteIconClick", "event", "stopPropagation", "handleKeyDown", "currentTarget", "target", "preventDefault", "handleKeyUp", "current", "blur", "isValidElement", "moreProps", "focusVisibleClassName", "disable<PERSON><PERSON><PERSON>", "cloneElement", "process", "env", "NODE_ENV", "console", "error", "as", "undefined", "children", "propTypes", "element", "object", "string", "bool", "oneOfType", "oneOf", "elementType", "node", "func", "sx", "arrayOf", "number"], "sources": ["D:/Safety Tracking Web App/node_modules/@mui/material/Chip/Chip.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"avatar\", \"className\", \"clickable\", \"color\", \"component\", \"deleteIcon\", \"disabled\", \"icon\", \"label\", \"onClick\", \"onDelete\", \"onKeyDown\", \"onKeyUp\", \"size\", \"variant\", \"tabIndex\", \"skipFocusWhenDisabled\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport CancelIcon from '../internal/svg-icons/Cancel';\nimport useForkRef from '../utils/useForkRef';\nimport unsupportedProp from '../utils/unsupportedProp';\nimport capitalize from '../utils/capitalize';\nimport ButtonBase from '../ButtonBase';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport chipClasses, { getChipUtilityClass } from './chipClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disabled,\n    size,\n    color,\n    iconColor,\n    onDelete,\n    clickable,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, disabled && 'disabled', `size${capitalize(size)}`, `color${capitalize(color)}`, clickable && 'clickable', clickable && `clickableColor${capitalize(color)}`, onDelete && 'deletable', onDelete && `deletableColor${capitalize(color)}`, `${variant}${capitalize(color)}`],\n    label: ['label', `label${capitalize(size)}`],\n    avatar: ['avatar', `avatar${capitalize(size)}`, `avatarColor${capitalize(color)}`],\n    icon: ['icon', `icon${capitalize(size)}`, `iconColor${capitalize(iconColor)}`],\n    deleteIcon: ['deleteIcon', `deleteIcon${capitalize(size)}`, `deleteIconColor${capitalize(color)}`, `deleteIcon${capitalize(variant)}Color${capitalize(color)}`]\n  };\n  return composeClasses(slots, getChipUtilityClass, classes);\n};\nconst ChipRoot = styled('div', {\n  name: 'MuiChip',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      color,\n      iconColor,\n      clickable,\n      onDelete,\n      size,\n      variant\n    } = ownerState;\n    return [{\n      [`& .${chipClasses.avatar}`]: styles.avatar\n    }, {\n      [`& .${chipClasses.avatar}`]: styles[`avatar${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.avatar}`]: styles[`avatarColor${capitalize(color)}`]\n    }, {\n      [`& .${chipClasses.icon}`]: styles.icon\n    }, {\n      [`& .${chipClasses.icon}`]: styles[`icon${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.icon}`]: styles[`iconColor${capitalize(iconColor)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles.deleteIcon\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIcon${capitalize(size)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIconColor${capitalize(color)}`]\n    }, {\n      [`& .${chipClasses.deleteIcon}`]: styles[`deleteIcon${capitalize(variant)}Color${capitalize(color)}`]\n    }, styles.root, styles[`size${capitalize(size)}`], styles[`color${capitalize(color)}`], clickable && styles.clickable, clickable && color !== 'default' && styles[`clickableColor${capitalize(color)})`], onDelete && styles.deletable, onDelete && color !== 'default' && styles[`deletableColor${capitalize(color)}`], styles[variant], styles[`${variant}${capitalize(color)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  const textColor = theme.palette.mode === 'light' ? theme.palette.grey[700] : theme.palette.grey[300];\n  return _extends({\n    maxWidth: '100%',\n    fontFamily: theme.typography.fontFamily,\n    fontSize: theme.typography.pxToRem(13),\n    display: 'inline-flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    height: 32,\n    color: (theme.vars || theme).palette.text.primary,\n    backgroundColor: (theme.vars || theme).palette.action.selected,\n    borderRadius: 32 / 2,\n    whiteSpace: 'nowrap',\n    transition: theme.transitions.create(['background-color', 'box-shadow']),\n    // reset cursor explicitly in case ButtonBase is used\n    cursor: 'unset',\n    // We disable the focus ring for mouse, touch and keyboard users.\n    outline: 0,\n    textDecoration: 'none',\n    border: 0,\n    // Remove `button` border\n    padding: 0,\n    // Remove `button` padding\n    verticalAlign: 'middle',\n    boxSizing: 'border-box',\n    [`&.${chipClasses.disabled}`]: {\n      opacity: (theme.vars || theme).palette.action.disabledOpacity,\n      pointerEvents: 'none'\n    },\n    [`& .${chipClasses.avatar}`]: {\n      marginLeft: 5,\n      marginRight: -6,\n      width: 24,\n      height: 24,\n      color: theme.vars ? theme.vars.palette.Chip.defaultAvatarColor : textColor,\n      fontSize: theme.typography.pxToRem(12)\n    },\n    [`& .${chipClasses.avatarColorPrimary}`]: {\n      color: (theme.vars || theme).palette.primary.contrastText,\n      backgroundColor: (theme.vars || theme).palette.primary.dark\n    },\n    [`& .${chipClasses.avatarColorSecondary}`]: {\n      color: (theme.vars || theme).palette.secondary.contrastText,\n      backgroundColor: (theme.vars || theme).palette.secondary.dark\n    },\n    [`& .${chipClasses.avatarSmall}`]: {\n      marginLeft: 4,\n      marginRight: -4,\n      width: 18,\n      height: 18,\n      fontSize: theme.typography.pxToRem(10)\n    },\n    [`& .${chipClasses.icon}`]: _extends({\n      marginLeft: 5,\n      marginRight: -6\n    }, ownerState.size === 'small' && {\n      fontSize: 18,\n      marginLeft: 4,\n      marginRight: -4\n    }, ownerState.iconColor === ownerState.color && _extends({\n      color: theme.vars ? theme.vars.palette.Chip.defaultIconColor : textColor\n    }, ownerState.color !== 'default' && {\n      color: 'inherit'\n    })),\n    [`& .${chipClasses.deleteIcon}`]: _extends({\n      WebkitTapHighlightColor: 'transparent',\n      color: theme.vars ? `rgba(${theme.vars.palette.text.primaryChannel} / 0.26)` : alpha(theme.palette.text.primary, 0.26),\n      fontSize: 22,\n      cursor: 'pointer',\n      margin: '0 5px 0 -6px',\n      '&:hover': {\n        color: theme.vars ? `rgba(${theme.vars.palette.text.primaryChannel} / 0.4)` : alpha(theme.palette.text.primary, 0.4)\n      }\n    }, ownerState.size === 'small' && {\n      fontSize: 16,\n      marginRight: 4,\n      marginLeft: -4\n    }, ownerState.color !== 'default' && {\n      color: theme.vars ? `rgba(${theme.vars.palette[ownerState.color].contrastTextChannel} / 0.7)` : alpha(theme.palette[ownerState.color].contrastText, 0.7),\n      '&:hover, &:active': {\n        color: (theme.vars || theme).palette[ownerState.color].contrastText\n      }\n    })\n  }, ownerState.size === 'small' && {\n    height: 24\n  }, ownerState.color !== 'default' && {\n    backgroundColor: (theme.vars || theme).palette[ownerState.color].main,\n    color: (theme.vars || theme).palette[ownerState.color].contrastText\n  }, ownerState.onDelete && {\n    [`&.${chipClasses.focusVisible}`]: {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n    }\n  }, ownerState.onDelete && ownerState.color !== 'default' && {\n    [`&.${chipClasses.focusVisible}`]: {\n      backgroundColor: (theme.vars || theme).palette[ownerState.color].dark\n    }\n  });\n}, ({\n  theme,\n  ownerState\n}) => _extends({}, ownerState.clickable && {\n  userSelect: 'none',\n  WebkitTapHighlightColor: 'transparent',\n  cursor: 'pointer',\n  '&:hover': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity)\n  },\n  [`&.${chipClasses.focusVisible}`]: {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n  },\n  '&:active': {\n    boxShadow: (theme.vars || theme).shadows[1]\n  }\n}, ownerState.clickable && ownerState.color !== 'default' && {\n  [`&:hover, &.${chipClasses.focusVisible}`]: {\n    backgroundColor: (theme.vars || theme).palette[ownerState.color].dark\n  }\n}), ({\n  theme,\n  ownerState\n}) => _extends({}, ownerState.variant === 'outlined' && {\n  backgroundColor: 'transparent',\n  border: theme.vars ? `1px solid ${theme.vars.palette.Chip.defaultBorder}` : `1px solid ${theme.palette.mode === 'light' ? theme.palette.grey[400] : theme.palette.grey[700]}`,\n  [`&.${chipClasses.clickable}:hover`]: {\n    backgroundColor: (theme.vars || theme).palette.action.hover\n  },\n  [`&.${chipClasses.focusVisible}`]: {\n    backgroundColor: (theme.vars || theme).palette.action.focus\n  },\n  [`& .${chipClasses.avatar}`]: {\n    marginLeft: 4\n  },\n  [`& .${chipClasses.avatarSmall}`]: {\n    marginLeft: 2\n  },\n  [`& .${chipClasses.icon}`]: {\n    marginLeft: 4\n  },\n  [`& .${chipClasses.iconSmall}`]: {\n    marginLeft: 2\n  },\n  [`& .${chipClasses.deleteIcon}`]: {\n    marginRight: 5\n  },\n  [`& .${chipClasses.deleteIconSmall}`]: {\n    marginRight: 3\n  }\n}, ownerState.variant === 'outlined' && ownerState.color !== 'default' && {\n  color: (theme.vars || theme).palette[ownerState.color].main,\n  border: `1px solid ${theme.vars ? `rgba(${theme.vars.palette[ownerState.color].mainChannel} / 0.7)` : alpha(theme.palette[ownerState.color].main, 0.7)}`,\n  [`&.${chipClasses.clickable}:hover`]: {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette[ownerState.color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[ownerState.color].main, theme.palette.action.hoverOpacity)\n  },\n  [`&.${chipClasses.focusVisible}`]: {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette[ownerState.color].mainChannel} / ${theme.vars.palette.action.focusOpacity})` : alpha(theme.palette[ownerState.color].main, theme.palette.action.focusOpacity)\n  },\n  [`& .${chipClasses.deleteIcon}`]: {\n    color: theme.vars ? `rgba(${theme.vars.palette[ownerState.color].mainChannel} / 0.7)` : alpha(theme.palette[ownerState.color].main, 0.7),\n    '&:hover, &:active': {\n      color: (theme.vars || theme).palette[ownerState.color].main\n    }\n  }\n}));\nconst ChipLabel = styled('span', {\n  name: 'MuiChip',\n  slot: 'Label',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      size\n    } = ownerState;\n    return [styles.label, styles[`label${capitalize(size)}`]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  overflow: 'hidden',\n  textOverflow: 'ellipsis',\n  paddingLeft: 12,\n  paddingRight: 12,\n  whiteSpace: 'nowrap'\n}, ownerState.variant === 'outlined' && {\n  paddingLeft: 11,\n  paddingRight: 11\n}, ownerState.size === 'small' && {\n  paddingLeft: 8,\n  paddingRight: 8\n}, ownerState.size === 'small' && ownerState.variant === 'outlined' && {\n  paddingLeft: 7,\n  paddingRight: 7\n}));\nfunction isDeleteKeyboardEvent(keyboardEvent) {\n  return keyboardEvent.key === 'Backspace' || keyboardEvent.key === 'Delete';\n}\n\n/**\n * Chips represent complex entities in small blocks, such as a contact.\n */\nconst Chip = /*#__PURE__*/React.forwardRef(function Chip(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiChip'\n  });\n  const {\n      avatar: avatarProp,\n      className,\n      clickable: clickableProp,\n      color = 'default',\n      component: ComponentProp,\n      deleteIcon: deleteIconProp,\n      disabled = false,\n      icon: iconProp,\n      label,\n      onClick,\n      onDelete,\n      onKeyDown,\n      onKeyUp,\n      size = 'medium',\n      variant = 'filled',\n      tabIndex,\n      skipFocusWhenDisabled = false // TODO v6: Rename to `focusableWhenDisabled`.\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const chipRef = React.useRef(null);\n  const handleRef = useForkRef(chipRef, ref);\n  const handleDeleteIconClick = event => {\n    // Stop the event from bubbling up to the `Chip`\n    event.stopPropagation();\n    if (onDelete) {\n      onDelete(event);\n    }\n  };\n  const handleKeyDown = event => {\n    // Ignore events from children of `Chip`.\n    if (event.currentTarget === event.target && isDeleteKeyboardEvent(event)) {\n      // Will be handled in keyUp, otherwise some browsers\n      // might init navigation\n      event.preventDefault();\n    }\n    if (onKeyDown) {\n      onKeyDown(event);\n    }\n  };\n  const handleKeyUp = event => {\n    // Ignore events from children of `Chip`.\n    if (event.currentTarget === event.target) {\n      if (onDelete && isDeleteKeyboardEvent(event)) {\n        onDelete(event);\n      } else if (event.key === 'Escape' && chipRef.current) {\n        chipRef.current.blur();\n      }\n    }\n    if (onKeyUp) {\n      onKeyUp(event);\n    }\n  };\n  const clickable = clickableProp !== false && onClick ? true : clickableProp;\n  const component = clickable || onDelete ? ButtonBase : ComponentProp || 'div';\n  const ownerState = _extends({}, props, {\n    component,\n    disabled,\n    size,\n    color,\n    iconColor: /*#__PURE__*/React.isValidElement(iconProp) ? iconProp.props.color || color : color,\n    onDelete: !!onDelete,\n    clickable,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const moreProps = component === ButtonBase ? _extends({\n    component: ComponentProp || 'div',\n    focusVisibleClassName: classes.focusVisible\n  }, onDelete && {\n    disableRipple: true\n  }) : {};\n  let deleteIcon = null;\n  if (onDelete) {\n    deleteIcon = deleteIconProp && /*#__PURE__*/React.isValidElement(deleteIconProp) ? ( /*#__PURE__*/React.cloneElement(deleteIconProp, {\n      className: clsx(deleteIconProp.props.className, classes.deleteIcon),\n      onClick: handleDeleteIconClick\n    })) : /*#__PURE__*/_jsx(CancelIcon, {\n      className: clsx(classes.deleteIcon),\n      onClick: handleDeleteIconClick\n    });\n  }\n  let avatar = null;\n  if (avatarProp && /*#__PURE__*/React.isValidElement(avatarProp)) {\n    avatar = /*#__PURE__*/React.cloneElement(avatarProp, {\n      className: clsx(classes.avatar, avatarProp.props.className)\n    });\n  }\n  let icon = null;\n  if (iconProp && /*#__PURE__*/React.isValidElement(iconProp)) {\n    icon = /*#__PURE__*/React.cloneElement(iconProp, {\n      className: clsx(classes.icon, iconProp.props.className)\n    });\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (avatar && icon) {\n      console.error('MUI: The Chip component can not handle the avatar ' + 'and the icon prop at the same time. Pick one.');\n    }\n  }\n  return /*#__PURE__*/_jsxs(ChipRoot, _extends({\n    as: component,\n    className: clsx(classes.root, className),\n    disabled: clickable && disabled ? true : undefined,\n    onClick: onClick,\n    onKeyDown: handleKeyDown,\n    onKeyUp: handleKeyUp,\n    ref: handleRef,\n    tabIndex: skipFocusWhenDisabled && disabled ? -1 : tabIndex,\n    ownerState: ownerState\n  }, moreProps, other, {\n    children: [avatar || icon, /*#__PURE__*/_jsx(ChipLabel, {\n      className: clsx(classes.label),\n      ownerState: ownerState,\n      children: label\n    }), deleteIcon]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Chip.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The Avatar element to display.\n   */\n  avatar: PropTypes.element,\n  /**\n   * This prop isn't supported.\n   * Use the `component` prop if you need to change the children structure.\n   */\n  children: unsupportedProp,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the chip will appear clickable, and will raise when pressed,\n   * even if the onClick prop is not defined.\n   * If `false`, the chip will not appear clickable, even if onClick prop is defined.\n   * This can be used, for example,\n   * along with the component prop to indicate an anchor Chip is clickable.\n   * Note: this controls the UI and does not affect the onClick event.\n   */\n  clickable: PropTypes.bool,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'default'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Override the default delete icon element. Shown only if `onDelete` is set.\n   */\n  deleteIcon: PropTypes.element,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Icon element.\n   */\n  icon: PropTypes.element,\n  /**\n   * The content of the component.\n   */\n  label: PropTypes.node,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * Callback fired when the delete icon is clicked.\n   * If set, the delete icon will be shown.\n   */\n  onDelete: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyUp: PropTypes.func,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * If `true`, allows the disabled chip to escape focus.\n   * If `false`, allows the disabled chip to receive focus.\n   * @default false\n   */\n  skipFocusWhenDisabled: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * @ignore\n   */\n  tabIndex: PropTypes.number,\n  /**\n   * The variant to use.\n   * @default 'filled'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['filled', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Chip;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,uBAAuB,CAAC;AAC9N,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,KAAK,QAAQ,8BAA8B;AACpD,OAAOC,UAAU,MAAM,8BAA8B;AACrD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,WAAW,IAAIC,mBAAmB,QAAQ,eAAe;AAChE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,QAAQ;IACRC,IAAI;IACJC,KAAK;IACLC,SAAS;IACTC,QAAQ;IACRC,SAAS;IACTC;EACF,CAAC,GAAGR,UAAU;EACd,MAAMS,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,OAAO,EAAEN,QAAQ,IAAI,UAAU,EAAE,OAAOb,UAAU,CAACc,IAAI,CAAC,EAAE,EAAE,QAAQd,UAAU,CAACe,KAAK,CAAC,EAAE,EAAEG,SAAS,IAAI,WAAW,EAAEA,SAAS,IAAI,iBAAiBlB,UAAU,CAACe,KAAK,CAAC,EAAE,EAAEE,QAAQ,IAAI,WAAW,EAAEA,QAAQ,IAAI,iBAAiBjB,UAAU,CAACe,KAAK,CAAC,EAAE,EAAE,GAAGI,OAAO,GAAGnB,UAAU,CAACe,KAAK,CAAC,EAAE,CAAC;IACjSO,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQtB,UAAU,CAACc,IAAI,CAAC,EAAE,CAAC;IAC5CS,MAAM,EAAE,CAAC,QAAQ,EAAE,SAASvB,UAAU,CAACc,IAAI,CAAC,EAAE,EAAE,cAAcd,UAAU,CAACe,KAAK,CAAC,EAAE,CAAC;IAClFS,IAAI,EAAE,CAAC,MAAM,EAAE,OAAOxB,UAAU,CAACc,IAAI,CAAC,EAAE,EAAE,YAAYd,UAAU,CAACgB,SAAS,CAAC,EAAE,CAAC;IAC9ES,UAAU,EAAE,CAAC,YAAY,EAAE,aAAazB,UAAU,CAACc,IAAI,CAAC,EAAE,EAAE,kBAAkBd,UAAU,CAACe,KAAK,CAAC,EAAE,EAAE,aAAaf,UAAU,CAACmB,OAAO,CAAC,QAAQnB,UAAU,CAACe,KAAK,CAAC,EAAE;EAChK,CAAC;EACD,OAAOpB,cAAc,CAACyB,KAAK,EAAEf,mBAAmB,EAAEO,OAAO,CAAC;AAC5D,CAAC;AACD,MAAMc,QAAQ,GAAGvB,MAAM,CAAC,KAAK,EAAE;EAC7BwB,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJpB;IACF,CAAC,GAAGmB,KAAK;IACT,MAAM;MACJf,KAAK;MACLC,SAAS;MACTE,SAAS;MACTD,QAAQ;MACRH,IAAI;MACJK;IACF,CAAC,GAAGR,UAAU;IACd,OAAO,CAAC;MACN,CAAC,MAAMP,WAAW,CAACmB,MAAM,EAAE,GAAGQ,MAAM,CAACR;IACvC,CAAC,EAAE;MACD,CAAC,MAAMnB,WAAW,CAACmB,MAAM,EAAE,GAAGQ,MAAM,CAAC,SAAS/B,UAAU,CAACc,IAAI,CAAC,EAAE;IAClE,CAAC,EAAE;MACD,CAAC,MAAMV,WAAW,CAACmB,MAAM,EAAE,GAAGQ,MAAM,CAAC,cAAc/B,UAAU,CAACe,KAAK,CAAC,EAAE;IACxE,CAAC,EAAE;MACD,CAAC,MAAMX,WAAW,CAACoB,IAAI,EAAE,GAAGO,MAAM,CAACP;IACrC,CAAC,EAAE;MACD,CAAC,MAAMpB,WAAW,CAACoB,IAAI,EAAE,GAAGO,MAAM,CAAC,OAAO/B,UAAU,CAACc,IAAI,CAAC,EAAE;IAC9D,CAAC,EAAE;MACD,CAAC,MAAMV,WAAW,CAACoB,IAAI,EAAE,GAAGO,MAAM,CAAC,YAAY/B,UAAU,CAACgB,SAAS,CAAC,EAAE;IACxE,CAAC,EAAE;MACD,CAAC,MAAMZ,WAAW,CAACqB,UAAU,EAAE,GAAGM,MAAM,CAACN;IAC3C,CAAC,EAAE;MACD,CAAC,MAAMrB,WAAW,CAACqB,UAAU,EAAE,GAAGM,MAAM,CAAC,aAAa/B,UAAU,CAACc,IAAI,CAAC,EAAE;IAC1E,CAAC,EAAE;MACD,CAAC,MAAMV,WAAW,CAACqB,UAAU,EAAE,GAAGM,MAAM,CAAC,kBAAkB/B,UAAU,CAACe,KAAK,CAAC,EAAE;IAChF,CAAC,EAAE;MACD,CAAC,MAAMX,WAAW,CAACqB,UAAU,EAAE,GAAGM,MAAM,CAAC,aAAa/B,UAAU,CAACmB,OAAO,CAAC,QAAQnB,UAAU,CAACe,KAAK,CAAC,EAAE;IACtG,CAAC,EAAEgB,MAAM,CAACV,IAAI,EAAEU,MAAM,CAAC,OAAO/B,UAAU,CAACc,IAAI,CAAC,EAAE,CAAC,EAAEiB,MAAM,CAAC,QAAQ/B,UAAU,CAACe,KAAK,CAAC,EAAE,CAAC,EAAEG,SAAS,IAAIa,MAAM,CAACb,SAAS,EAAEA,SAAS,IAAIH,KAAK,KAAK,SAAS,IAAIgB,MAAM,CAAC,iBAAiB/B,UAAU,CAACe,KAAK,CAAC,GAAG,CAAC,EAAEE,QAAQ,IAAIc,MAAM,CAACC,SAAS,EAAEf,QAAQ,IAAIF,KAAK,KAAK,SAAS,IAAIgB,MAAM,CAAC,iBAAiB/B,UAAU,CAACe,KAAK,CAAC,EAAE,CAAC,EAAEgB,MAAM,CAACZ,OAAO,CAAC,EAAEY,MAAM,CAAC,GAAGZ,OAAO,GAAGnB,UAAU,CAACe,KAAK,CAAC,EAAE,CAAC,CAAC;EACrX;AACF,CAAC,CAAC,CAAC,CAAC;EACFkB,KAAK;EACLtB;AACF,CAAC,KAAK;EACJ,MAAMuB,SAAS,GAAGD,KAAK,CAACE,OAAO,CAACC,IAAI,KAAK,OAAO,GAAGH,KAAK,CAACE,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC,GAAGJ,KAAK,CAACE,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC;EACpG,OAAO/C,QAAQ,CAAC;IACdgD,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAEN,KAAK,CAACO,UAAU,CAACD,UAAU;IACvCE,QAAQ,EAAER,KAAK,CAACO,UAAU,CAACE,OAAO,CAAC,EAAE,CAAC;IACtCC,OAAO,EAAE,aAAa;IACtBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE,EAAE;IACV/B,KAAK,EAAE,CAACkB,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAACa,IAAI,CAACC,OAAO;IACjDC,eAAe,EAAE,CAACjB,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAACgB,MAAM,CAACC,QAAQ;IAC9DC,YAAY,EAAE,EAAE,GAAG,CAAC;IACpBC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAEtB,KAAK,CAACuB,WAAW,CAACC,MAAM,CAAC,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;IACxE;IACAC,MAAM,EAAE,OAAO;IACf;IACAC,OAAO,EAAE,CAAC;IACVC,cAAc,EAAE,MAAM;IACtBC,MAAM,EAAE,CAAC;IACT;IACAC,OAAO,EAAE,CAAC;IACV;IACAC,aAAa,EAAE,QAAQ;IACvBC,SAAS,EAAE,YAAY;IACvB,CAAC,KAAK5D,WAAW,CAACS,QAAQ,EAAE,GAAG;MAC7BoD,OAAO,EAAE,CAAChC,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAACgB,MAAM,CAACe,eAAe;MAC7DC,aAAa,EAAE;IACjB,CAAC;IACD,CAAC,MAAM/D,WAAW,CAACmB,MAAM,EAAE,GAAG;MAC5B6C,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC,CAAC;MACfC,KAAK,EAAE,EAAE;MACTxB,MAAM,EAAE,EAAE;MACV/B,KAAK,EAAEkB,KAAK,CAACc,IAAI,GAAGd,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACoC,IAAI,CAACC,kBAAkB,GAAGtC,SAAS;MAC1EO,QAAQ,EAAER,KAAK,CAACO,UAAU,CAACE,OAAO,CAAC,EAAE;IACvC,CAAC;IACD,CAAC,MAAMtC,WAAW,CAACqE,kBAAkB,EAAE,GAAG;MACxC1D,KAAK,EAAE,CAACkB,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAACc,OAAO,CAACyB,YAAY;MACzDxB,eAAe,EAAE,CAACjB,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAACc,OAAO,CAAC0B;IACzD,CAAC;IACD,CAAC,MAAMvE,WAAW,CAACwE,oBAAoB,EAAE,GAAG;MAC1C7D,KAAK,EAAE,CAACkB,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAAC0C,SAAS,CAACH,YAAY;MAC3DxB,eAAe,EAAE,CAACjB,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAAC0C,SAAS,CAACF;IAC3D,CAAC;IACD,CAAC,MAAMvE,WAAW,CAAC0E,WAAW,EAAE,GAAG;MACjCV,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC,CAAC;MACfC,KAAK,EAAE,EAAE;MACTxB,MAAM,EAAE,EAAE;MACVL,QAAQ,EAAER,KAAK,CAACO,UAAU,CAACE,OAAO,CAAC,EAAE;IACvC,CAAC;IACD,CAAC,MAAMtC,WAAW,CAACoB,IAAI,EAAE,GAAGlC,QAAQ,CAAC;MACnC8E,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;IAChB,CAAC,EAAE1D,UAAU,CAACG,IAAI,KAAK,OAAO,IAAI;MAChC2B,QAAQ,EAAE,EAAE;MACZ2B,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;IAChB,CAAC,EAAE1D,UAAU,CAACK,SAAS,KAAKL,UAAU,CAACI,KAAK,IAAIzB,QAAQ,CAAC;MACvDyB,KAAK,EAAEkB,KAAK,CAACc,IAAI,GAAGd,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACoC,IAAI,CAACQ,gBAAgB,GAAG7C;IACjE,CAAC,EAAEvB,UAAU,CAACI,KAAK,KAAK,SAAS,IAAI;MACnCA,KAAK,EAAE;IACT,CAAC,CAAC,CAAC;IACH,CAAC,MAAMX,WAAW,CAACqB,UAAU,EAAE,GAAGnC,QAAQ,CAAC;MACzC0F,uBAAuB,EAAE,aAAa;MACtCjE,KAAK,EAAEkB,KAAK,CAACc,IAAI,GAAG,QAAQd,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACa,IAAI,CAACiC,cAAc,UAAU,GAAGrF,KAAK,CAACqC,KAAK,CAACE,OAAO,CAACa,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC;MACtHR,QAAQ,EAAE,EAAE;MACZiB,MAAM,EAAE,SAAS;MACjBwB,MAAM,EAAE,cAAc;MACtB,SAAS,EAAE;QACTnE,KAAK,EAAEkB,KAAK,CAACc,IAAI,GAAG,QAAQd,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACa,IAAI,CAACiC,cAAc,SAAS,GAAGrF,KAAK,CAACqC,KAAK,CAACE,OAAO,CAACa,IAAI,CAACC,OAAO,EAAE,GAAG;MACrH;IACF,CAAC,EAAEtC,UAAU,CAACG,IAAI,KAAK,OAAO,IAAI;MAChC2B,QAAQ,EAAE,EAAE;MACZ4B,WAAW,EAAE,CAAC;MACdD,UAAU,EAAE,CAAC;IACf,CAAC,EAAEzD,UAAU,CAACI,KAAK,KAAK,SAAS,IAAI;MACnCA,KAAK,EAAEkB,KAAK,CAACc,IAAI,GAAG,QAAQd,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAACoE,mBAAmB,SAAS,GAAGvF,KAAK,CAACqC,KAAK,CAACE,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAAC2D,YAAY,EAAE,GAAG,CAAC;MACxJ,mBAAmB,EAAE;QACnB3D,KAAK,EAAE,CAACkB,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAAC2D;MACzD;IACF,CAAC;EACH,CAAC,EAAE/D,UAAU,CAACG,IAAI,KAAK,OAAO,IAAI;IAChCgC,MAAM,EAAE;EACV,CAAC,EAAEnC,UAAU,CAACI,KAAK,KAAK,SAAS,IAAI;IACnCmC,eAAe,EAAE,CAACjB,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAACqE,IAAI;IACrErE,KAAK,EAAE,CAACkB,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAAC2D;EACzD,CAAC,EAAE/D,UAAU,CAACM,QAAQ,IAAI;IACxB,CAAC,KAAKb,WAAW,CAACiF,YAAY,EAAE,GAAG;MACjCnC,eAAe,EAAEjB,KAAK,CAACc,IAAI,GAAG,QAAQd,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACgB,MAAM,CAACmC,eAAe,WAAWrD,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACgB,MAAM,CAACoC,eAAe,MAAMtD,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACgB,MAAM,CAACqC,YAAY,IAAI,GAAG5F,KAAK,CAACqC,KAAK,CAACE,OAAO,CAACgB,MAAM,CAACC,QAAQ,EAAEnB,KAAK,CAACE,OAAO,CAACgB,MAAM,CAACoC,eAAe,GAAGtD,KAAK,CAACE,OAAO,CAACgB,MAAM,CAACqC,YAAY;IACrS;EACF,CAAC,EAAE7E,UAAU,CAACM,QAAQ,IAAIN,UAAU,CAACI,KAAK,KAAK,SAAS,IAAI;IAC1D,CAAC,KAAKX,WAAW,CAACiF,YAAY,EAAE,GAAG;MACjCnC,eAAe,EAAE,CAACjB,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAAC4D;IACnE;EACF,CAAC,CAAC;AACJ,CAAC,EAAE,CAAC;EACF1C,KAAK;EACLtB;AACF,CAAC,KAAKrB,QAAQ,CAAC,CAAC,CAAC,EAAEqB,UAAU,CAACO,SAAS,IAAI;EACzCuE,UAAU,EAAE,MAAM;EAClBT,uBAAuB,EAAE,aAAa;EACtCtB,MAAM,EAAE,SAAS;EACjB,SAAS,EAAE;IACTR,eAAe,EAAEjB,KAAK,CAACc,IAAI,GAAG,QAAQd,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACgB,MAAM,CAACmC,eAAe,WAAWrD,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACgB,MAAM,CAACoC,eAAe,MAAMtD,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACgB,MAAM,CAACuC,YAAY,IAAI,GAAG9F,KAAK,CAACqC,KAAK,CAACE,OAAO,CAACgB,MAAM,CAACC,QAAQ,EAAEnB,KAAK,CAACE,OAAO,CAACgB,MAAM,CAACoC,eAAe,GAAGtD,KAAK,CAACE,OAAO,CAACgB,MAAM,CAACuC,YAAY;EACrS,CAAC;EACD,CAAC,KAAKtF,WAAW,CAACiF,YAAY,EAAE,GAAG;IACjCnC,eAAe,EAAEjB,KAAK,CAACc,IAAI,GAAG,QAAQd,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACgB,MAAM,CAACmC,eAAe,WAAWrD,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACgB,MAAM,CAACoC,eAAe,MAAMtD,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACgB,MAAM,CAACqC,YAAY,IAAI,GAAG5F,KAAK,CAACqC,KAAK,CAACE,OAAO,CAACgB,MAAM,CAACC,QAAQ,EAAEnB,KAAK,CAACE,OAAO,CAACgB,MAAM,CAACoC,eAAe,GAAGtD,KAAK,CAACE,OAAO,CAACgB,MAAM,CAACqC,YAAY;EACrS,CAAC;EACD,UAAU,EAAE;IACVG,SAAS,EAAE,CAAC1D,KAAK,CAACc,IAAI,IAAId,KAAK,EAAE2D,OAAO,CAAC,CAAC;EAC5C;AACF,CAAC,EAAEjF,UAAU,CAACO,SAAS,IAAIP,UAAU,CAACI,KAAK,KAAK,SAAS,IAAI;EAC3D,CAAC,cAAcX,WAAW,CAACiF,YAAY,EAAE,GAAG;IAC1CnC,eAAe,EAAE,CAACjB,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAAC4D;EACnE;AACF,CAAC,CAAC,EAAE,CAAC;EACH1C,KAAK;EACLtB;AACF,CAAC,KAAKrB,QAAQ,CAAC,CAAC,CAAC,EAAEqB,UAAU,CAACQ,OAAO,KAAK,UAAU,IAAI;EACtD+B,eAAe,EAAE,aAAa;EAC9BW,MAAM,EAAE5B,KAAK,CAACc,IAAI,GAAG,aAAad,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACoC,IAAI,CAACsB,aAAa,EAAE,GAAG,aAAa5D,KAAK,CAACE,OAAO,CAACC,IAAI,KAAK,OAAO,GAAGH,KAAK,CAACE,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC,GAAGJ,KAAK,CAACE,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC,EAAE;EAC7K,CAAC,KAAKjC,WAAW,CAACc,SAAS,QAAQ,GAAG;IACpCgC,eAAe,EAAE,CAACjB,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAACgB,MAAM,CAAC2C;EACxD,CAAC;EACD,CAAC,KAAK1F,WAAW,CAACiF,YAAY,EAAE,GAAG;IACjCnC,eAAe,EAAE,CAACjB,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAACgB,MAAM,CAAC4C;EACxD,CAAC;EACD,CAAC,MAAM3F,WAAW,CAACmB,MAAM,EAAE,GAAG;IAC5B6C,UAAU,EAAE;EACd,CAAC;EACD,CAAC,MAAMhE,WAAW,CAAC0E,WAAW,EAAE,GAAG;IACjCV,UAAU,EAAE;EACd,CAAC;EACD,CAAC,MAAMhE,WAAW,CAACoB,IAAI,EAAE,GAAG;IAC1B4C,UAAU,EAAE;EACd,CAAC;EACD,CAAC,MAAMhE,WAAW,CAAC4F,SAAS,EAAE,GAAG;IAC/B5B,UAAU,EAAE;EACd,CAAC;EACD,CAAC,MAAMhE,WAAW,CAACqB,UAAU,EAAE,GAAG;IAChC4C,WAAW,EAAE;EACf,CAAC;EACD,CAAC,MAAMjE,WAAW,CAAC6F,eAAe,EAAE,GAAG;IACrC5B,WAAW,EAAE;EACf;AACF,CAAC,EAAE1D,UAAU,CAACQ,OAAO,KAAK,UAAU,IAAIR,UAAU,CAACI,KAAK,KAAK,SAAS,IAAI;EACxEA,KAAK,EAAE,CAACkB,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAACqE,IAAI;EAC3DvB,MAAM,EAAE,aAAa5B,KAAK,CAACc,IAAI,GAAG,QAAQd,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAACmF,WAAW,SAAS,GAAGtG,KAAK,CAACqC,KAAK,CAACE,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAACqE,IAAI,EAAE,GAAG,CAAC,EAAE;EACxJ,CAAC,KAAKhF,WAAW,CAACc,SAAS,QAAQ,GAAG;IACpCgC,eAAe,EAAEjB,KAAK,CAACc,IAAI,GAAG,QAAQd,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAACmF,WAAW,MAAMjE,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACgB,MAAM,CAACuC,YAAY,GAAG,GAAG9F,KAAK,CAACqC,KAAK,CAACE,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAACqE,IAAI,EAAEnD,KAAK,CAACE,OAAO,CAACgB,MAAM,CAACuC,YAAY;EACvN,CAAC;EACD,CAAC,KAAKtF,WAAW,CAACiF,YAAY,EAAE,GAAG;IACjCnC,eAAe,EAAEjB,KAAK,CAACc,IAAI,GAAG,QAAQd,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAACmF,WAAW,MAAMjE,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACgB,MAAM,CAACqC,YAAY,GAAG,GAAG5F,KAAK,CAACqC,KAAK,CAACE,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAACqE,IAAI,EAAEnD,KAAK,CAACE,OAAO,CAACgB,MAAM,CAACqC,YAAY;EACvN,CAAC;EACD,CAAC,MAAMpF,WAAW,CAACqB,UAAU,EAAE,GAAG;IAChCV,KAAK,EAAEkB,KAAK,CAACc,IAAI,GAAG,QAAQd,KAAK,CAACc,IAAI,CAACZ,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAACmF,WAAW,SAAS,GAAGtG,KAAK,CAACqC,KAAK,CAACE,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAACqE,IAAI,EAAE,GAAG,CAAC;IACxI,mBAAmB,EAAE;MACnBrE,KAAK,EAAE,CAACkB,KAAK,CAACc,IAAI,IAAId,KAAK,EAAEE,OAAO,CAACxB,UAAU,CAACI,KAAK,CAAC,CAACqE;IACzD;EACF;AACF,CAAC,CAAC,CAAC;AACH,MAAMe,SAAS,GAAGhG,MAAM,CAAC,MAAM,EAAE;EAC/BwB,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJpB;IACF,CAAC,GAAGmB,KAAK;IACT,MAAM;MACJhB;IACF,CAAC,GAAGH,UAAU;IACd,OAAO,CAACoB,MAAM,CAACT,KAAK,EAAES,MAAM,CAAC,QAAQ/B,UAAU,CAACc,IAAI,CAAC,EAAE,CAAC,CAAC;EAC3D;AACF,CAAC,CAAC,CAAC,CAAC;EACFH;AACF,CAAC,KAAKrB,QAAQ,CAAC;EACb8G,QAAQ,EAAE,QAAQ;EAClBC,YAAY,EAAE,UAAU;EACxBC,WAAW,EAAE,EAAE;EACfC,YAAY,EAAE,EAAE;EAChBjD,UAAU,EAAE;AACd,CAAC,EAAE3C,UAAU,CAACQ,OAAO,KAAK,UAAU,IAAI;EACtCmF,WAAW,EAAE,EAAE;EACfC,YAAY,EAAE;AAChB,CAAC,EAAE5F,UAAU,CAACG,IAAI,KAAK,OAAO,IAAI;EAChCwF,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE;AAChB,CAAC,EAAE5F,UAAU,CAACG,IAAI,KAAK,OAAO,IAAIH,UAAU,CAACQ,OAAO,KAAK,UAAU,IAAI;EACrEmF,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE;AAChB,CAAC,CAAC,CAAC;AACH,SAASC,qBAAqBA,CAACC,aAAa,EAAE;EAC5C,OAAOA,aAAa,CAACC,GAAG,KAAK,WAAW,IAAID,aAAa,CAACC,GAAG,KAAK,QAAQ;AAC5E;;AAEA;AACA;AACA;AACA,MAAMnC,IAAI,GAAG,aAAa/E,KAAK,CAACmH,UAAU,CAAC,SAASpC,IAAIA,CAACqC,OAAO,EAAEC,GAAG,EAAE;EACrE,MAAM/E,KAAK,GAAG5B,eAAe,CAAC;IAC5B4B,KAAK,EAAE8E,OAAO;IACdjF,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFJ,MAAM,EAAEuF,UAAU;MAClBC,SAAS;MACT7F,SAAS,EAAE8F,aAAa;MACxBjG,KAAK,GAAG,SAAS;MACjBkG,SAAS,EAAEC,aAAa;MACxBzF,UAAU,EAAE0F,cAAc;MAC1BtG,QAAQ,GAAG,KAAK;MAChBW,IAAI,EAAE4F,QAAQ;MACd9F,KAAK;MACL+F,OAAO;MACPpG,QAAQ;MACRqG,SAAS;MACTC,OAAO;MACPzG,IAAI,GAAG,QAAQ;MACfK,OAAO,GAAG,QAAQ;MAClBqG,QAAQ;MACRC,qBAAqB,GAAG,KAAK,CAAC;IAChC,CAAC,GAAG3F,KAAK;IACT4F,KAAK,GAAGrI,6BAA6B,CAACyC,KAAK,EAAEvC,SAAS,CAAC;EACzD,MAAMoI,OAAO,GAAGnI,KAAK,CAACoI,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMC,SAAS,GAAG/H,UAAU,CAAC6H,OAAO,EAAEd,GAAG,CAAC;EAC1C,MAAMiB,qBAAqB,GAAGC,KAAK,IAAI;IACrC;IACAA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI/G,QAAQ,EAAE;MACZA,QAAQ,CAAC8G,KAAK,CAAC;IACjB;EACF,CAAC;EACD,MAAME,aAAa,GAAGF,KAAK,IAAI;IAC7B;IACA,IAAIA,KAAK,CAACG,aAAa,KAAKH,KAAK,CAACI,MAAM,IAAI3B,qBAAqB,CAACuB,KAAK,CAAC,EAAE;MACxE;MACA;MACAA,KAAK,CAACK,cAAc,CAAC,CAAC;IACxB;IACA,IAAId,SAAS,EAAE;MACbA,SAAS,CAACS,KAAK,CAAC;IAClB;EACF,CAAC;EACD,MAAMM,WAAW,GAAGN,KAAK,IAAI;IAC3B;IACA,IAAIA,KAAK,CAACG,aAAa,KAAKH,KAAK,CAACI,MAAM,EAAE;MACxC,IAAIlH,QAAQ,IAAIuF,qBAAqB,CAACuB,KAAK,CAAC,EAAE;QAC5C9G,QAAQ,CAAC8G,KAAK,CAAC;MACjB,CAAC,MAAM,IAAIA,KAAK,CAACrB,GAAG,KAAK,QAAQ,IAAIiB,OAAO,CAACW,OAAO,EAAE;QACpDX,OAAO,CAACW,OAAO,CAACC,IAAI,CAAC,CAAC;MACxB;IACF;IACA,IAAIhB,OAAO,EAAE;MACXA,OAAO,CAACQ,KAAK,CAAC;IAChB;EACF,CAAC;EACD,MAAM7G,SAAS,GAAG8F,aAAa,KAAK,KAAK,IAAIK,OAAO,GAAG,IAAI,GAAGL,aAAa;EAC3E,MAAMC,SAAS,GAAG/F,SAAS,IAAID,QAAQ,GAAGhB,UAAU,GAAGiH,aAAa,IAAI,KAAK;EAC7E,MAAMvG,UAAU,GAAGrB,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;IACrCmF,SAAS;IACTpG,QAAQ;IACRC,IAAI;IACJC,KAAK;IACLC,SAAS,EAAE,aAAaxB,KAAK,CAACgJ,cAAc,CAACpB,QAAQ,CAAC,GAAGA,QAAQ,CAACtF,KAAK,CAACf,KAAK,IAAIA,KAAK,GAAGA,KAAK;IAC9FE,QAAQ,EAAE,CAAC,CAACA,QAAQ;IACpBC,SAAS;IACTC;EACF,CAAC,CAAC;EACF,MAAMP,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM8H,SAAS,GAAGxB,SAAS,KAAKhH,UAAU,GAAGX,QAAQ,CAAC;IACpD2H,SAAS,EAAEC,aAAa,IAAI,KAAK;IACjCwB,qBAAqB,EAAE9H,OAAO,CAACyE;EACjC,CAAC,EAAEpE,QAAQ,IAAI;IACb0H,aAAa,EAAE;EACjB,CAAC,CAAC,GAAG,CAAC,CAAC;EACP,IAAIlH,UAAU,GAAG,IAAI;EACrB,IAAIR,QAAQ,EAAE;IACZQ,UAAU,GAAG0F,cAAc,IAAI,aAAa3H,KAAK,CAACgJ,cAAc,CAACrB,cAAc,CAAC,IAAK,aAAa3H,KAAK,CAACoJ,YAAY,CAACzB,cAAc,EAAE;MACnIJ,SAAS,EAAErH,IAAI,CAACyH,cAAc,CAACrF,KAAK,CAACiF,SAAS,EAAEnG,OAAO,CAACa,UAAU,CAAC;MACnE4F,OAAO,EAAES;IACX,CAAC,CAAC,IAAI,aAAavH,IAAI,CAACV,UAAU,EAAE;MAClCkH,SAAS,EAAErH,IAAI,CAACkB,OAAO,CAACa,UAAU,CAAC;MACnC4F,OAAO,EAAES;IACX,CAAC,CAAC;EACJ;EACA,IAAIvG,MAAM,GAAG,IAAI;EACjB,IAAIuF,UAAU,IAAI,aAAatH,KAAK,CAACgJ,cAAc,CAAC1B,UAAU,CAAC,EAAE;IAC/DvF,MAAM,GAAG,aAAa/B,KAAK,CAACoJ,YAAY,CAAC9B,UAAU,EAAE;MACnDC,SAAS,EAAErH,IAAI,CAACkB,OAAO,CAACW,MAAM,EAAEuF,UAAU,CAAChF,KAAK,CAACiF,SAAS;IAC5D,CAAC,CAAC;EACJ;EACA,IAAIvF,IAAI,GAAG,IAAI;EACf,IAAI4F,QAAQ,IAAI,aAAa5H,KAAK,CAACgJ,cAAc,CAACpB,QAAQ,CAAC,EAAE;IAC3D5F,IAAI,GAAG,aAAahC,KAAK,CAACoJ,YAAY,CAACxB,QAAQ,EAAE;MAC/CL,SAAS,EAAErH,IAAI,CAACkB,OAAO,CAACY,IAAI,EAAE4F,QAAQ,CAACtF,KAAK,CAACiF,SAAS;IACxD,CAAC,CAAC;EACJ;EACA,IAAI8B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIxH,MAAM,IAAIC,IAAI,EAAE;MAClBwH,OAAO,CAACC,KAAK,CAAC,oDAAoD,GAAG,+CAA+C,CAAC;IACvH;EACF;EACA,OAAO,aAAaxI,KAAK,CAACiB,QAAQ,EAAEpC,QAAQ,CAAC;IAC3C4J,EAAE,EAAEjC,SAAS;IACbF,SAAS,EAAErH,IAAI,CAACkB,OAAO,CAACS,IAAI,EAAE0F,SAAS,CAAC;IACxClG,QAAQ,EAAEK,SAAS,IAAIL,QAAQ,GAAG,IAAI,GAAGsI,SAAS;IAClD9B,OAAO,EAAEA,OAAO;IAChBC,SAAS,EAAEW,aAAa;IACxBV,OAAO,EAAEc,WAAW;IACpBxB,GAAG,EAAEgB,SAAS;IACdL,QAAQ,EAAEC,qBAAqB,IAAI5G,QAAQ,GAAG,CAAC,CAAC,GAAG2G,QAAQ;IAC3D7G,UAAU,EAAEA;EACd,CAAC,EAAE8H,SAAS,EAAEf,KAAK,EAAE;IACnB0B,QAAQ,EAAE,CAAC7H,MAAM,IAAIC,IAAI,EAAE,aAAajB,IAAI,CAAC4F,SAAS,EAAE;MACtDY,SAAS,EAAErH,IAAI,CAACkB,OAAO,CAACU,KAAK,CAAC;MAC9BX,UAAU,EAAEA,UAAU;MACtByI,QAAQ,EAAE9H;IACZ,CAAC,CAAC,EAAEG,UAAU;EAChB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFoH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxE,IAAI,CAAC8E,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;EACE9H,MAAM,EAAE9B,SAAS,CAAC6J,OAAO;EACzB;AACF;AACA;AACA;EACEF,QAAQ,EAAErJ,eAAe;EACzB;AACF;AACA;EACEa,OAAO,EAAEnB,SAAS,CAAC8J,MAAM;EACzB;AACF;AACA;EACExC,SAAS,EAAEtH,SAAS,CAAC+J,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEtI,SAAS,EAAEzB,SAAS,CAACgK,IAAI;EACzB;AACF;AACA;AACA;AACA;AACA;EACE1I,KAAK,EAAEtB,SAAS,CAAC,sCAAsCiK,SAAS,CAAC,CAACjK,SAAS,CAACkK,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAElK,SAAS,CAAC+J,MAAM,CAAC,CAAC;EACjL;AACF;AACA;AACA;EACEvC,SAAS,EAAExH,SAAS,CAACmK,WAAW;EAChC;AACF;AACA;EACEnI,UAAU,EAAEhC,SAAS,CAAC6J,OAAO;EAC7B;AACF;AACA;AACA;EACEzI,QAAQ,EAAEpB,SAAS,CAACgK,IAAI;EACxB;AACF;AACA;EACEjI,IAAI,EAAE/B,SAAS,CAAC6J,OAAO;EACvB;AACF;AACA;EACEhI,KAAK,EAAE7B,SAAS,CAACoK,IAAI;EACrB;AACF;AACA;EACExC,OAAO,EAAE5H,SAAS,CAACqK,IAAI;EACvB;AACF;AACA;AACA;EACE7I,QAAQ,EAAExB,SAAS,CAACqK,IAAI;EACxB;AACF;AACA;EACExC,SAAS,EAAE7H,SAAS,CAACqK,IAAI;EACzB;AACF;AACA;EACEvC,OAAO,EAAE9H,SAAS,CAACqK,IAAI;EACvB;AACF;AACA;AACA;EACEhJ,IAAI,EAAErB,SAAS,CAAC,sCAAsCiK,SAAS,CAAC,CAACjK,SAAS,CAACkK,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAElK,SAAS,CAAC+J,MAAM,CAAC,CAAC;EACzH;AACF;AACA;AACA;AACA;EACE/B,qBAAqB,EAAEhI,SAAS,CAACgK,IAAI;EACrC;AACF;AACA;EACEM,EAAE,EAAEtK,SAAS,CAACiK,SAAS,CAAC,CAACjK,SAAS,CAACuK,OAAO,CAACvK,SAAS,CAACiK,SAAS,CAAC,CAACjK,SAAS,CAACqK,IAAI,EAAErK,SAAS,CAAC8J,MAAM,EAAE9J,SAAS,CAACgK,IAAI,CAAC,CAAC,CAAC,EAAEhK,SAAS,CAACqK,IAAI,EAAErK,SAAS,CAAC8J,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACE/B,QAAQ,EAAE/H,SAAS,CAACwK,MAAM;EAC1B;AACF;AACA;AACA;EACE9I,OAAO,EAAE1B,SAAS,CAAC,sCAAsCiK,SAAS,CAAC,CAACjK,SAAS,CAACkK,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAElK,SAAS,CAAC+J,MAAM,CAAC;AAChI,CAAC,GAAG,KAAK,CAAC;AACV,eAAejF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}