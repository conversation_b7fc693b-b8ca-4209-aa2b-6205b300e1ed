{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"value\"],\n  _excluded2 = [\"className\", \"defaultValue\", \"disabled\", \"emptyIcon\", \"emptyLabelText\", \"getLabelText\", \"highlightSelectedOnly\", \"icon\", \"IconContainerComponent\", \"max\", \"name\", \"onChange\", \"onChangeActive\", \"onMouseLeave\", \"onMouseMove\", \"precision\", \"readOnly\", \"size\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport clamp from '@mui/utils/clamp';\nimport visuallyHidden from '@mui/utils/visuallyHidden';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { capitalize, useForkRef, useIsFocusVisible, useControlled, unstable_useId as useId } from '../utils';\nimport Star from '../internal/svg-icons/Star';\nimport StarBorder from '../internal/svg-icons/StarBorder';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled, { slotShouldForwardProp } from '../styles/styled';\nimport ratingClasses, { getRatingUtilityClass } from './ratingClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction getDecimalPrecision(num) {\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToPrecision(value, precision) {\n  if (value == null) {\n    return value;\n  }\n  const nearest = Math.round(value / precision) * precision;\n  return Number(nearest.toFixed(getDecimalPrecision(precision)));\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    size,\n    readOnly,\n    disabled,\n    emptyValueFocused,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', `size${capitalize(size)}`, disabled && 'disabled', focusVisible && 'focusVisible', readOnly && 'readOnly'],\n    label: ['label', 'pristine'],\n    labelEmptyValue: [emptyValueFocused && 'labelEmptyValueActive'],\n    icon: ['icon'],\n    iconEmpty: ['iconEmpty'],\n    iconFilled: ['iconFilled'],\n    iconHover: ['iconHover'],\n    iconFocus: ['iconFocus'],\n    iconActive: ['iconActive'],\n    decimal: ['decimal'],\n    visuallyHidden: ['visuallyHidden']\n  };\n  return composeClasses(slots, getRatingUtilityClass, classes);\n};\nconst RatingRoot = styled('span', {\n  name: 'MuiRating',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${ratingClasses.visuallyHidden}`]: styles.visuallyHidden\n    }, styles.root, styles[`size${capitalize(ownerState.size)}`], ownerState.readOnly && styles.readOnly];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  display: 'inline-flex',\n  // Required to position the pristine input absolutely\n  position: 'relative',\n  fontSize: theme.typography.pxToRem(24),\n  color: '#faaf00',\n  cursor: 'pointer',\n  textAlign: 'left',\n  width: 'min-content',\n  WebkitTapHighlightColor: 'transparent',\n  [`&.${ratingClasses.disabled}`]: {\n    opacity: (theme.vars || theme).palette.action.disabledOpacity,\n    pointerEvents: 'none'\n  },\n  [`&.${ratingClasses.focusVisible} .${ratingClasses.iconActive}`]: {\n    outline: '1px solid #999'\n  },\n  [`& .${ratingClasses.visuallyHidden}`]: visuallyHidden\n}, ownerState.size === 'small' && {\n  fontSize: theme.typography.pxToRem(18)\n}, ownerState.size === 'large' && {\n  fontSize: theme.typography.pxToRem(30)\n}, ownerState.readOnly && {\n  pointerEvents: 'none'\n}));\nconst RatingLabel = styled('label', {\n  name: 'MuiRating',\n  slot: 'Label',\n  overridesResolver: ({\n    ownerState\n  }, styles) => [styles.label, ownerState.emptyValueFocused && styles.labelEmptyValueActive]\n})(({\n  ownerState\n}) => _extends({\n  cursor: 'inherit'\n}, ownerState.emptyValueFocused && {\n  top: 0,\n  bottom: 0,\n  position: 'absolute',\n  outline: '1px solid #999',\n  width: '100%'\n}));\nconst RatingIcon = styled('span', {\n  name: 'MuiRating',\n  slot: 'Icon',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.icon, ownerState.iconEmpty && styles.iconEmpty, ownerState.iconFilled && styles.iconFilled, ownerState.iconHover && styles.iconHover, ownerState.iconFocus && styles.iconFocus, ownerState.iconActive && styles.iconActive];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  // Fit wrapper to actual icon size.\n  display: 'flex',\n  transition: theme.transitions.create('transform', {\n    duration: theme.transitions.duration.shortest\n  }),\n  // Fix mouseLeave issue.\n  // https://github.com/facebook/react/issues/4492\n  pointerEvents: 'none'\n}, ownerState.iconActive && {\n  transform: 'scale(1.2)'\n}, ownerState.iconEmpty && {\n  color: (theme.vars || theme).palette.action.disabled\n}));\nconst RatingDecimal = styled('span', {\n  name: 'MuiRating',\n  slot: 'Decimal',\n  shouldForwardProp: prop => slotShouldForwardProp(prop) && prop !== 'iconActive',\n  overridesResolver: (props, styles) => {\n    const {\n      iconActive\n    } = props;\n    return [styles.decimal, iconActive && styles.iconActive];\n  }\n})(({\n  iconActive\n}) => _extends({\n  position: 'relative'\n}, iconActive && {\n  transform: 'scale(1.2)'\n}));\nfunction IconContainer(props) {\n  const other = _objectWithoutPropertiesLoose(props, _excluded);\n  return /*#__PURE__*/_jsx(\"span\", _extends({}, other));\n}\nprocess.env.NODE_ENV !== \"production\" ? IconContainer.propTypes = {\n  value: PropTypes.number.isRequired\n} : void 0;\nfunction RatingItem(props) {\n  const {\n    classes,\n    disabled,\n    emptyIcon,\n    focus,\n    getLabelText,\n    highlightSelectedOnly,\n    hover,\n    icon,\n    IconContainerComponent,\n    isActive,\n    itemValue,\n    labelProps,\n    name,\n    onBlur,\n    onChange,\n    onClick,\n    onFocus,\n    readOnly,\n    ownerState,\n    ratingValue,\n    ratingValueRounded\n  } = props;\n  const isFilled = highlightSelectedOnly ? itemValue === ratingValue : itemValue <= ratingValue;\n  const isHovered = itemValue <= hover;\n  const isFocused = itemValue <= focus;\n  const isChecked = itemValue === ratingValueRounded;\n  const id = useId();\n  const container = /*#__PURE__*/_jsx(RatingIcon, {\n    as: IconContainerComponent,\n    value: itemValue,\n    className: clsx(classes.icon, isFilled ? classes.iconFilled : classes.iconEmpty, isHovered && classes.iconHover, isFocused && classes.iconFocus, isActive && classes.iconActive),\n    ownerState: _extends({}, ownerState, {\n      iconEmpty: !isFilled,\n      iconFilled: isFilled,\n      iconHover: isHovered,\n      iconFocus: isFocused,\n      iconActive: isActive\n    }),\n    children: emptyIcon && !isFilled ? emptyIcon : icon\n  });\n  if (readOnly) {\n    return /*#__PURE__*/_jsx(\"span\", _extends({}, labelProps, {\n      children: container\n    }));\n  }\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsxs(RatingLabel, _extends({\n      ownerState: _extends({}, ownerState, {\n        emptyValueFocused: undefined\n      }),\n      htmlFor: id\n    }, labelProps, {\n      children: [container, /*#__PURE__*/_jsx(\"span\", {\n        className: classes.visuallyHidden,\n        children: getLabelText(itemValue)\n      })]\n    })), /*#__PURE__*/_jsx(\"input\", {\n      className: classes.visuallyHidden,\n      onFocus: onFocus,\n      onBlur: onBlur,\n      onChange: onChange,\n      onClick: onClick,\n      disabled: disabled,\n      value: itemValue,\n      id: id,\n      type: \"radio\",\n      name: name,\n      checked: isChecked\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? RatingItem.propTypes = {\n  classes: PropTypes.object.isRequired,\n  disabled: PropTypes.bool.isRequired,\n  emptyIcon: PropTypes.node,\n  focus: PropTypes.number.isRequired,\n  getLabelText: PropTypes.func.isRequired,\n  highlightSelectedOnly: PropTypes.bool.isRequired,\n  hover: PropTypes.number.isRequired,\n  icon: PropTypes.node,\n  IconContainerComponent: PropTypes.elementType.isRequired,\n  isActive: PropTypes.bool.isRequired,\n  itemValue: PropTypes.number.isRequired,\n  labelProps: PropTypes.object,\n  name: PropTypes.string,\n  onBlur: PropTypes.func.isRequired,\n  onChange: PropTypes.func.isRequired,\n  onClick: PropTypes.func.isRequired,\n  onFocus: PropTypes.func.isRequired,\n  ownerState: PropTypes.object.isRequired,\n  ratingValue: PropTypes.number,\n  ratingValueRounded: PropTypes.number,\n  readOnly: PropTypes.bool.isRequired\n} : void 0;\nconst defaultIcon = /*#__PURE__*/_jsx(Star, {\n  fontSize: \"inherit\"\n});\nconst defaultEmptyIcon = /*#__PURE__*/_jsx(StarBorder, {\n  fontSize: \"inherit\"\n});\nfunction defaultLabelText(value) {\n  return `${value} Star${value !== 1 ? 's' : ''}`;\n}\nconst Rating = /*#__PURE__*/React.forwardRef(function Rating(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiRating',\n    props: inProps\n  });\n  const {\n      className,\n      defaultValue = null,\n      disabled = false,\n      emptyIcon = defaultEmptyIcon,\n      emptyLabelText = 'Empty',\n      getLabelText = defaultLabelText,\n      highlightSelectedOnly = false,\n      icon = defaultIcon,\n      IconContainerComponent = IconContainer,\n      max = 5,\n      name: nameProp,\n      onChange,\n      onChangeActive,\n      onMouseLeave,\n      onMouseMove,\n      precision = 1,\n      readOnly = false,\n      size = 'medium',\n      value: valueProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const name = useId(nameProp);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'Rating'\n  });\n  const valueRounded = roundValueToPrecision(valueDerived, precision);\n  const isRtl = useRtl();\n  const [{\n    hover,\n    focus\n  }, setState] = React.useState({\n    hover: -1,\n    focus: -1\n  });\n  let value = valueRounded;\n  if (hover !== -1) {\n    value = hover;\n  }\n  if (focus !== -1) {\n    value = focus;\n  }\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  const rootRef = React.useRef();\n  const handleRef = useForkRef(focusVisibleRef, rootRef, ref);\n  const handleMouseMove = event => {\n    if (onMouseMove) {\n      onMouseMove(event);\n    }\n    const rootNode = rootRef.current;\n    const {\n      right,\n      left,\n      width: containerWidth\n    } = rootNode.getBoundingClientRect();\n    let percent;\n    if (isRtl) {\n      percent = (right - event.clientX) / containerWidth;\n    } else {\n      percent = (event.clientX - left) / containerWidth;\n    }\n    let newHover = roundValueToPrecision(max * percent + precision / 2, precision);\n    newHover = clamp(newHover, precision, max);\n    setState(prev => prev.hover === newHover && prev.focus === newHover ? prev : {\n      hover: newHover,\n      focus: newHover\n    });\n    setFocusVisible(false);\n    if (onChangeActive && hover !== newHover) {\n      onChangeActive(event, newHover);\n    }\n  };\n  const handleMouseLeave = event => {\n    if (onMouseLeave) {\n      onMouseLeave(event);\n    }\n    const newHover = -1;\n    setState({\n      hover: newHover,\n      focus: newHover\n    });\n    if (onChangeActive && hover !== newHover) {\n      onChangeActive(event, newHover);\n    }\n  };\n  const handleChange = event => {\n    let newValue = event.target.value === '' ? null : parseFloat(event.target.value);\n\n    // Give mouse priority over keyboard\n    // Fix https://github.com/mui/material-ui/issues/22827\n    if (hover !== -1) {\n      newValue = hover;\n    }\n    setValueState(newValue);\n    if (onChange) {\n      onChange(event, newValue);\n    }\n  };\n  const handleClear = event => {\n    // Ignore keyboard events\n    // https://github.com/facebook/react/issues/7407\n    if (event.clientX === 0 && event.clientY === 0) {\n      return;\n    }\n    setState({\n      hover: -1,\n      focus: -1\n    });\n    setValueState(null);\n    if (onChange && parseFloat(event.target.value) === valueRounded) {\n      onChange(event, null);\n    }\n  };\n  const handleFocus = event => {\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusVisible(true);\n    }\n    const newFocus = parseFloat(event.target.value);\n    setState(prev => ({\n      hover: prev.hover,\n      focus: newFocus\n    }));\n  };\n  const handleBlur = event => {\n    if (hover !== -1) {\n      return;\n    }\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    const newFocus = -1;\n    setState(prev => ({\n      hover: prev.hover,\n      focus: newFocus\n    }));\n  };\n  const [emptyValueFocused, setEmptyValueFocused] = React.useState(false);\n  const ownerState = _extends({}, props, {\n    defaultValue,\n    disabled,\n    emptyIcon,\n    emptyLabelText,\n    emptyValueFocused,\n    focusVisible,\n    getLabelText,\n    icon,\n    IconContainerComponent,\n    max,\n    precision,\n    readOnly,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(RatingRoot, _extends({\n    ref: handleRef,\n    onMouseMove: handleMouseMove,\n    onMouseLeave: handleMouseLeave,\n    className: clsx(classes.root, className, readOnly && 'MuiRating-readOnly'),\n    ownerState: ownerState,\n    role: readOnly ? 'img' : null,\n    \"aria-label\": readOnly ? getLabelText(value) : null\n  }, other, {\n    children: [Array.from(new Array(max)).map((_, index) => {\n      const itemValue = index + 1;\n      const ratingItemProps = {\n        classes,\n        disabled,\n        emptyIcon,\n        focus,\n        getLabelText,\n        highlightSelectedOnly,\n        hover,\n        icon,\n        IconContainerComponent,\n        name,\n        onBlur: handleBlur,\n        onChange: handleChange,\n        onClick: handleClear,\n        onFocus: handleFocus,\n        ratingValue: value,\n        ratingValueRounded: valueRounded,\n        readOnly,\n        ownerState\n      };\n      const isActive = itemValue === Math.ceil(value) && (hover !== -1 || focus !== -1);\n      if (precision < 1) {\n        const items = Array.from(new Array(1 / precision));\n        return /*#__PURE__*/_jsx(RatingDecimal, {\n          className: clsx(classes.decimal, isActive && classes.iconActive),\n          ownerState: ownerState,\n          iconActive: isActive,\n          children: items.map(($, indexDecimal) => {\n            const itemDecimalValue = roundValueToPrecision(itemValue - 1 + (indexDecimal + 1) * precision, precision);\n            return /*#__PURE__*/_jsx(RatingItem, _extends({}, ratingItemProps, {\n              // The icon is already displayed as active\n              isActive: false,\n              itemValue: itemDecimalValue,\n              labelProps: {\n                style: items.length - 1 === indexDecimal ? {} : {\n                  width: itemDecimalValue === value ? `${(indexDecimal + 1) * precision * 100}%` : '0%',\n                  overflow: 'hidden',\n                  position: 'absolute'\n                }\n              }\n            }), itemDecimalValue);\n          })\n        }, itemValue);\n      }\n      return /*#__PURE__*/_jsx(RatingItem, _extends({}, ratingItemProps, {\n        isActive: isActive,\n        itemValue: itemValue\n      }), itemValue);\n    }), !readOnly && !disabled && /*#__PURE__*/_jsxs(RatingLabel, {\n      className: clsx(classes.label, classes.labelEmptyValue),\n      ownerState: ownerState,\n      children: [/*#__PURE__*/_jsx(\"input\", {\n        className: classes.visuallyHidden,\n        value: \"\",\n        id: `${name}-empty`,\n        type: \"radio\",\n        name: name,\n        checked: valueRounded == null,\n        onFocus: () => setEmptyValueFocused(true),\n        onBlur: () => setEmptyValueFocused(false),\n        onChange: handleChange\n      }), /*#__PURE__*/_jsx(\"span\", {\n        className: classes.visuallyHidden,\n        children: emptyLabelText\n      })]\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Rating.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The default value. Use when the component is not controlled.\n   * @default null\n   */\n  defaultValue: PropTypes.number,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * The icon to display when empty.\n   * @default <StarBorder fontSize=\"inherit\" />\n   */\n  emptyIcon: PropTypes.node,\n  /**\n   * The label read when the rating input is empty.\n   * @default 'Empty'\n   */\n  emptyLabelText: PropTypes.node,\n  /**\n   * Accepts a function which returns a string value that provides a user-friendly name for the current value of the rating.\n   * This is important for screen reader users.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @param {number} value The rating label's value to format.\n   * @returns {string}\n   * @default function defaultLabelText(value) {\n   *   return `${value} Star${value !== 1 ? 's' : ''}`;\n   * }\n   */\n  getLabelText: PropTypes.func,\n  /**\n   * If `true`, only the selected icon will be highlighted.\n   * @default false\n   */\n  highlightSelectedOnly: PropTypes.bool,\n  /**\n   * The icon to display.\n   * @default <Star fontSize=\"inherit\" />\n   */\n  icon: PropTypes.node,\n  /**\n   * The component containing the icon.\n   * @default function IconContainer(props) {\n   *   const { value, ...other } = props;\n   *   return <span {...other} />;\n   * }\n   */\n  IconContainerComponent: PropTypes.elementType,\n  /**\n   * Maximum rating.\n   * @default 5\n   */\n  max: PropTypes.number,\n  /**\n   * The name attribute of the radio `input` elements.\n   * This input `name` should be unique within the page.\n   * Being unique within a form is insufficient since the `name` is used to generated IDs.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value changes.\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {number|null} value The new value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback function that is fired when the hover state changes.\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {number} value The new value.\n   */\n  onChangeActive: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseLeave: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseMove: PropTypes.func,\n  /**\n   * The minimum increment value change allowed.\n   * @default 1\n   */\n  precision: chainPropTypes(PropTypes.number, props => {\n    if (props.precision < 0.1) {\n      return new Error(['MUI: The prop `precision` should be above 0.1.', 'A value below this limit has an imperceptible impact.'].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * Removes all hover effects and pointer events.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium', 'large']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The rating value.\n   */\n  value: PropTypes.number\n} : void 0;\nexport default Rating;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "_excluded2", "React", "PropTypes", "clsx", "clamp", "visuallyHidden", "chainPropTypes", "composeClasses", "useRtl", "capitalize", "useForkRef", "useIsFocusVisible", "useControlled", "unstable_useId", "useId", "Star", "StarBorder", "useDefaultProps", "styled", "slotShouldForwardProp", "ratingClasses", "getRatingUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "getDecimalPrecision", "num", "decimalPart", "toString", "split", "length", "roundValueToPrecision", "value", "precision", "nearest", "Math", "round", "Number", "toFixed", "useUtilityClasses", "ownerState", "classes", "size", "readOnly", "disabled", "emptyValueFocused", "focusVisible", "slots", "root", "label", "labelEmptyValue", "icon", "iconEmpty", "iconFilled", "iconHover", "iconFocus", "iconActive", "decimal", "RatingRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "display", "position", "fontSize", "typography", "pxToRem", "color", "cursor", "textAlign", "width", "WebkitTapHighlightColor", "opacity", "vars", "palette", "action", "disabledOpacity", "pointerEvents", "outline", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labelEmptyValueActive", "top", "bottom", "RatingIcon", "transition", "transitions", "create", "duration", "shortest", "transform", "RatingDecimal", "shouldForwardProp", "prop", "IconContainer", "other", "process", "env", "NODE_ENV", "propTypes", "number", "isRequired", "RatingItem", "emptyIcon", "focus", "getLabelText", "highlightSelectedOnly", "hover", "IconContainerComponent", "isActive", "itemValue", "labelProps", "onBlur", "onChange", "onClick", "onFocus", "ratingValue", "ratingValueRounded", "isFilled", "isHovered", "isFocused", "isChecked", "id", "container", "as", "className", "children", "Fragment", "undefined", "htmlFor", "type", "checked", "object", "bool", "node", "func", "elementType", "string", "defaultIcon", "defaultEmptyIcon", "defaultLabelText", "Rating", "forwardRef", "inProps", "ref", "defaultValue", "emptyLabelText", "max", "nameProp", "onChangeActive", "onMouseLeave", "onMouseMove", "valueProp", "valueDerived", "setValueState", "controlled", "default", "valueRounded", "isRtl", "setState", "useState", "isFocusVisibleRef", "handleBlurVisible", "handleFocusVisible", "focusVisibleRef", "setFocusVisible", "rootRef", "useRef", "handleRef", "handleMouseMove", "event", "rootNode", "current", "right", "left", "containerWidth", "getBoundingClientRect", "percent", "clientX", "newHover", "prev", "handleMouseLeave", "handleChange", "newValue", "target", "parseFloat", "handleClear", "clientY", "handleFocus", "newFocus", "handleBlur", "setEmptyValueFocused", "role", "Array", "from", "map", "_", "index", "ratingItemProps", "ceil", "items", "$", "indexDecimal", "itemDecimalValue", "style", "overflow", "Error", "join", "oneOfType", "oneOf", "sx", "arrayOf"], "sources": ["D:/Safety Tracking Web App/node_modules/@mui/material/Rating/Rating.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"value\"],\n  _excluded2 = [\"className\", \"defaultValue\", \"disabled\", \"emptyIcon\", \"emptyLabelText\", \"getLabelText\", \"highlightSelectedOnly\", \"icon\", \"IconContainerComponent\", \"max\", \"name\", \"onChange\", \"onChangeActive\", \"onMouseLeave\", \"onMouseMove\", \"precision\", \"readOnly\", \"size\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport clamp from '@mui/utils/clamp';\nimport visuallyHidden from '@mui/utils/visuallyHidden';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { capitalize, useForkRef, useIsFocusVisible, useControlled, unstable_useId as useId } from '../utils';\nimport Star from '../internal/svg-icons/Star';\nimport StarBorder from '../internal/svg-icons/StarBorder';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled, { slotShouldForwardProp } from '../styles/styled';\nimport ratingClasses, { getRatingUtilityClass } from './ratingClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction getDecimalPrecision(num) {\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToPrecision(value, precision) {\n  if (value == null) {\n    return value;\n  }\n  const nearest = Math.round(value / precision) * precision;\n  return Number(nearest.toFixed(getDecimalPrecision(precision)));\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    size,\n    readOnly,\n    disabled,\n    emptyValueFocused,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', `size${capitalize(size)}`, disabled && 'disabled', focusVisible && 'focusVisible', readOnly && 'readOnly'],\n    label: ['label', 'pristine'],\n    labelEmptyValue: [emptyValueFocused && 'labelEmptyValueActive'],\n    icon: ['icon'],\n    iconEmpty: ['iconEmpty'],\n    iconFilled: ['iconFilled'],\n    iconHover: ['iconHover'],\n    iconFocus: ['iconFocus'],\n    iconActive: ['iconActive'],\n    decimal: ['decimal'],\n    visuallyHidden: ['visuallyHidden']\n  };\n  return composeClasses(slots, getRatingUtilityClass, classes);\n};\nconst RatingRoot = styled('span', {\n  name: 'MuiRating',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${ratingClasses.visuallyHidden}`]: styles.visuallyHidden\n    }, styles.root, styles[`size${capitalize(ownerState.size)}`], ownerState.readOnly && styles.readOnly];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  display: 'inline-flex',\n  // Required to position the pristine input absolutely\n  position: 'relative',\n  fontSize: theme.typography.pxToRem(24),\n  color: '#faaf00',\n  cursor: 'pointer',\n  textAlign: 'left',\n  width: 'min-content',\n  WebkitTapHighlightColor: 'transparent',\n  [`&.${ratingClasses.disabled}`]: {\n    opacity: (theme.vars || theme).palette.action.disabledOpacity,\n    pointerEvents: 'none'\n  },\n  [`&.${ratingClasses.focusVisible} .${ratingClasses.iconActive}`]: {\n    outline: '1px solid #999'\n  },\n  [`& .${ratingClasses.visuallyHidden}`]: visuallyHidden\n}, ownerState.size === 'small' && {\n  fontSize: theme.typography.pxToRem(18)\n}, ownerState.size === 'large' && {\n  fontSize: theme.typography.pxToRem(30)\n}, ownerState.readOnly && {\n  pointerEvents: 'none'\n}));\nconst RatingLabel = styled('label', {\n  name: 'MuiRating',\n  slot: 'Label',\n  overridesResolver: ({\n    ownerState\n  }, styles) => [styles.label, ownerState.emptyValueFocused && styles.labelEmptyValueActive]\n})(({\n  ownerState\n}) => _extends({\n  cursor: 'inherit'\n}, ownerState.emptyValueFocused && {\n  top: 0,\n  bottom: 0,\n  position: 'absolute',\n  outline: '1px solid #999',\n  width: '100%'\n}));\nconst RatingIcon = styled('span', {\n  name: 'MuiRating',\n  slot: 'Icon',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.icon, ownerState.iconEmpty && styles.iconEmpty, ownerState.iconFilled && styles.iconFilled, ownerState.iconHover && styles.iconHover, ownerState.iconFocus && styles.iconFocus, ownerState.iconActive && styles.iconActive];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  // Fit wrapper to actual icon size.\n  display: 'flex',\n  transition: theme.transitions.create('transform', {\n    duration: theme.transitions.duration.shortest\n  }),\n  // Fix mouseLeave issue.\n  // https://github.com/facebook/react/issues/4492\n  pointerEvents: 'none'\n}, ownerState.iconActive && {\n  transform: 'scale(1.2)'\n}, ownerState.iconEmpty && {\n  color: (theme.vars || theme).palette.action.disabled\n}));\nconst RatingDecimal = styled('span', {\n  name: 'MuiRating',\n  slot: 'Decimal',\n  shouldForwardProp: prop => slotShouldForwardProp(prop) && prop !== 'iconActive',\n  overridesResolver: (props, styles) => {\n    const {\n      iconActive\n    } = props;\n    return [styles.decimal, iconActive && styles.iconActive];\n  }\n})(({\n  iconActive\n}) => _extends({\n  position: 'relative'\n}, iconActive && {\n  transform: 'scale(1.2)'\n}));\nfunction IconContainer(props) {\n  const other = _objectWithoutPropertiesLoose(props, _excluded);\n  return /*#__PURE__*/_jsx(\"span\", _extends({}, other));\n}\nprocess.env.NODE_ENV !== \"production\" ? IconContainer.propTypes = {\n  value: PropTypes.number.isRequired\n} : void 0;\nfunction RatingItem(props) {\n  const {\n    classes,\n    disabled,\n    emptyIcon,\n    focus,\n    getLabelText,\n    highlightSelectedOnly,\n    hover,\n    icon,\n    IconContainerComponent,\n    isActive,\n    itemValue,\n    labelProps,\n    name,\n    onBlur,\n    onChange,\n    onClick,\n    onFocus,\n    readOnly,\n    ownerState,\n    ratingValue,\n    ratingValueRounded\n  } = props;\n  const isFilled = highlightSelectedOnly ? itemValue === ratingValue : itemValue <= ratingValue;\n  const isHovered = itemValue <= hover;\n  const isFocused = itemValue <= focus;\n  const isChecked = itemValue === ratingValueRounded;\n  const id = useId();\n  const container = /*#__PURE__*/_jsx(RatingIcon, {\n    as: IconContainerComponent,\n    value: itemValue,\n    className: clsx(classes.icon, isFilled ? classes.iconFilled : classes.iconEmpty, isHovered && classes.iconHover, isFocused && classes.iconFocus, isActive && classes.iconActive),\n    ownerState: _extends({}, ownerState, {\n      iconEmpty: !isFilled,\n      iconFilled: isFilled,\n      iconHover: isHovered,\n      iconFocus: isFocused,\n      iconActive: isActive\n    }),\n    children: emptyIcon && !isFilled ? emptyIcon : icon\n  });\n  if (readOnly) {\n    return /*#__PURE__*/_jsx(\"span\", _extends({}, labelProps, {\n      children: container\n    }));\n  }\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsxs(RatingLabel, _extends({\n      ownerState: _extends({}, ownerState, {\n        emptyValueFocused: undefined\n      }),\n      htmlFor: id\n    }, labelProps, {\n      children: [container, /*#__PURE__*/_jsx(\"span\", {\n        className: classes.visuallyHidden,\n        children: getLabelText(itemValue)\n      })]\n    })), /*#__PURE__*/_jsx(\"input\", {\n      className: classes.visuallyHidden,\n      onFocus: onFocus,\n      onBlur: onBlur,\n      onChange: onChange,\n      onClick: onClick,\n      disabled: disabled,\n      value: itemValue,\n      id: id,\n      type: \"radio\",\n      name: name,\n      checked: isChecked\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? RatingItem.propTypes = {\n  classes: PropTypes.object.isRequired,\n  disabled: PropTypes.bool.isRequired,\n  emptyIcon: PropTypes.node,\n  focus: PropTypes.number.isRequired,\n  getLabelText: PropTypes.func.isRequired,\n  highlightSelectedOnly: PropTypes.bool.isRequired,\n  hover: PropTypes.number.isRequired,\n  icon: PropTypes.node,\n  IconContainerComponent: PropTypes.elementType.isRequired,\n  isActive: PropTypes.bool.isRequired,\n  itemValue: PropTypes.number.isRequired,\n  labelProps: PropTypes.object,\n  name: PropTypes.string,\n  onBlur: PropTypes.func.isRequired,\n  onChange: PropTypes.func.isRequired,\n  onClick: PropTypes.func.isRequired,\n  onFocus: PropTypes.func.isRequired,\n  ownerState: PropTypes.object.isRequired,\n  ratingValue: PropTypes.number,\n  ratingValueRounded: PropTypes.number,\n  readOnly: PropTypes.bool.isRequired\n} : void 0;\nconst defaultIcon = /*#__PURE__*/_jsx(Star, {\n  fontSize: \"inherit\"\n});\nconst defaultEmptyIcon = /*#__PURE__*/_jsx(StarBorder, {\n  fontSize: \"inherit\"\n});\nfunction defaultLabelText(value) {\n  return `${value} Star${value !== 1 ? 's' : ''}`;\n}\nconst Rating = /*#__PURE__*/React.forwardRef(function Rating(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiRating',\n    props: inProps\n  });\n  const {\n      className,\n      defaultValue = null,\n      disabled = false,\n      emptyIcon = defaultEmptyIcon,\n      emptyLabelText = 'Empty',\n      getLabelText = defaultLabelText,\n      highlightSelectedOnly = false,\n      icon = defaultIcon,\n      IconContainerComponent = IconContainer,\n      max = 5,\n      name: nameProp,\n      onChange,\n      onChangeActive,\n      onMouseLeave,\n      onMouseMove,\n      precision = 1,\n      readOnly = false,\n      size = 'medium',\n      value: valueProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const name = useId(nameProp);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'Rating'\n  });\n  const valueRounded = roundValueToPrecision(valueDerived, precision);\n  const isRtl = useRtl();\n  const [{\n    hover,\n    focus\n  }, setState] = React.useState({\n    hover: -1,\n    focus: -1\n  });\n  let value = valueRounded;\n  if (hover !== -1) {\n    value = hover;\n  }\n  if (focus !== -1) {\n    value = focus;\n  }\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  const rootRef = React.useRef();\n  const handleRef = useForkRef(focusVisibleRef, rootRef, ref);\n  const handleMouseMove = event => {\n    if (onMouseMove) {\n      onMouseMove(event);\n    }\n    const rootNode = rootRef.current;\n    const {\n      right,\n      left,\n      width: containerWidth\n    } = rootNode.getBoundingClientRect();\n    let percent;\n    if (isRtl) {\n      percent = (right - event.clientX) / containerWidth;\n    } else {\n      percent = (event.clientX - left) / containerWidth;\n    }\n    let newHover = roundValueToPrecision(max * percent + precision / 2, precision);\n    newHover = clamp(newHover, precision, max);\n    setState(prev => prev.hover === newHover && prev.focus === newHover ? prev : {\n      hover: newHover,\n      focus: newHover\n    });\n    setFocusVisible(false);\n    if (onChangeActive && hover !== newHover) {\n      onChangeActive(event, newHover);\n    }\n  };\n  const handleMouseLeave = event => {\n    if (onMouseLeave) {\n      onMouseLeave(event);\n    }\n    const newHover = -1;\n    setState({\n      hover: newHover,\n      focus: newHover\n    });\n    if (onChangeActive && hover !== newHover) {\n      onChangeActive(event, newHover);\n    }\n  };\n  const handleChange = event => {\n    let newValue = event.target.value === '' ? null : parseFloat(event.target.value);\n\n    // Give mouse priority over keyboard\n    // Fix https://github.com/mui/material-ui/issues/22827\n    if (hover !== -1) {\n      newValue = hover;\n    }\n    setValueState(newValue);\n    if (onChange) {\n      onChange(event, newValue);\n    }\n  };\n  const handleClear = event => {\n    // Ignore keyboard events\n    // https://github.com/facebook/react/issues/7407\n    if (event.clientX === 0 && event.clientY === 0) {\n      return;\n    }\n    setState({\n      hover: -1,\n      focus: -1\n    });\n    setValueState(null);\n    if (onChange && parseFloat(event.target.value) === valueRounded) {\n      onChange(event, null);\n    }\n  };\n  const handleFocus = event => {\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusVisible(true);\n    }\n    const newFocus = parseFloat(event.target.value);\n    setState(prev => ({\n      hover: prev.hover,\n      focus: newFocus\n    }));\n  };\n  const handleBlur = event => {\n    if (hover !== -1) {\n      return;\n    }\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    const newFocus = -1;\n    setState(prev => ({\n      hover: prev.hover,\n      focus: newFocus\n    }));\n  };\n  const [emptyValueFocused, setEmptyValueFocused] = React.useState(false);\n  const ownerState = _extends({}, props, {\n    defaultValue,\n    disabled,\n    emptyIcon,\n    emptyLabelText,\n    emptyValueFocused,\n    focusVisible,\n    getLabelText,\n    icon,\n    IconContainerComponent,\n    max,\n    precision,\n    readOnly,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(RatingRoot, _extends({\n    ref: handleRef,\n    onMouseMove: handleMouseMove,\n    onMouseLeave: handleMouseLeave,\n    className: clsx(classes.root, className, readOnly && 'MuiRating-readOnly'),\n    ownerState: ownerState,\n    role: readOnly ? 'img' : null,\n    \"aria-label\": readOnly ? getLabelText(value) : null\n  }, other, {\n    children: [Array.from(new Array(max)).map((_, index) => {\n      const itemValue = index + 1;\n      const ratingItemProps = {\n        classes,\n        disabled,\n        emptyIcon,\n        focus,\n        getLabelText,\n        highlightSelectedOnly,\n        hover,\n        icon,\n        IconContainerComponent,\n        name,\n        onBlur: handleBlur,\n        onChange: handleChange,\n        onClick: handleClear,\n        onFocus: handleFocus,\n        ratingValue: value,\n        ratingValueRounded: valueRounded,\n        readOnly,\n        ownerState\n      };\n      const isActive = itemValue === Math.ceil(value) && (hover !== -1 || focus !== -1);\n      if (precision < 1) {\n        const items = Array.from(new Array(1 / precision));\n        return /*#__PURE__*/_jsx(RatingDecimal, {\n          className: clsx(classes.decimal, isActive && classes.iconActive),\n          ownerState: ownerState,\n          iconActive: isActive,\n          children: items.map(($, indexDecimal) => {\n            const itemDecimalValue = roundValueToPrecision(itemValue - 1 + (indexDecimal + 1) * precision, precision);\n            return /*#__PURE__*/_jsx(RatingItem, _extends({}, ratingItemProps, {\n              // The icon is already displayed as active\n              isActive: false,\n              itemValue: itemDecimalValue,\n              labelProps: {\n                style: items.length - 1 === indexDecimal ? {} : {\n                  width: itemDecimalValue === value ? `${(indexDecimal + 1) * precision * 100}%` : '0%',\n                  overflow: 'hidden',\n                  position: 'absolute'\n                }\n              }\n            }), itemDecimalValue);\n          })\n        }, itemValue);\n      }\n      return /*#__PURE__*/_jsx(RatingItem, _extends({}, ratingItemProps, {\n        isActive: isActive,\n        itemValue: itemValue\n      }), itemValue);\n    }), !readOnly && !disabled && /*#__PURE__*/_jsxs(RatingLabel, {\n      className: clsx(classes.label, classes.labelEmptyValue),\n      ownerState: ownerState,\n      children: [/*#__PURE__*/_jsx(\"input\", {\n        className: classes.visuallyHidden,\n        value: \"\",\n        id: `${name}-empty`,\n        type: \"radio\",\n        name: name,\n        checked: valueRounded == null,\n        onFocus: () => setEmptyValueFocused(true),\n        onBlur: () => setEmptyValueFocused(false),\n        onChange: handleChange\n      }), /*#__PURE__*/_jsx(\"span\", {\n        className: classes.visuallyHidden,\n        children: emptyLabelText\n      })]\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Rating.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The default value. Use when the component is not controlled.\n   * @default null\n   */\n  defaultValue: PropTypes.number,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * The icon to display when empty.\n   * @default <StarBorder fontSize=\"inherit\" />\n   */\n  emptyIcon: PropTypes.node,\n  /**\n   * The label read when the rating input is empty.\n   * @default 'Empty'\n   */\n  emptyLabelText: PropTypes.node,\n  /**\n   * Accepts a function which returns a string value that provides a user-friendly name for the current value of the rating.\n   * This is important for screen reader users.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @param {number} value The rating label's value to format.\n   * @returns {string}\n   * @default function defaultLabelText(value) {\n   *   return `${value} Star${value !== 1 ? 's' : ''}`;\n   * }\n   */\n  getLabelText: PropTypes.func,\n  /**\n   * If `true`, only the selected icon will be highlighted.\n   * @default false\n   */\n  highlightSelectedOnly: PropTypes.bool,\n  /**\n   * The icon to display.\n   * @default <Star fontSize=\"inherit\" />\n   */\n  icon: PropTypes.node,\n  /**\n   * The component containing the icon.\n   * @default function IconContainer(props) {\n   *   const { value, ...other } = props;\n   *   return <span {...other} />;\n   * }\n   */\n  IconContainerComponent: PropTypes.elementType,\n  /**\n   * Maximum rating.\n   * @default 5\n   */\n  max: PropTypes.number,\n  /**\n   * The name attribute of the radio `input` elements.\n   * This input `name` should be unique within the page.\n   * Being unique within a form is insufficient since the `name` is used to generated IDs.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value changes.\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {number|null} value The new value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback function that is fired when the hover state changes.\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {number} value The new value.\n   */\n  onChangeActive: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseLeave: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseMove: PropTypes.func,\n  /**\n   * The minimum increment value change allowed.\n   * @default 1\n   */\n  precision: chainPropTypes(PropTypes.number, props => {\n    if (props.precision < 0.1) {\n      return new Error(['MUI: The prop `precision` should be above 0.1.', 'A value below this limit has an imperceptible impact.'].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * Removes all hover effects and pointer events.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium', 'large']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The rating value.\n   */\n  value: PropTypes.number\n} : void 0;\nexport default Rating;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,OAAO,CAAC;EACzBC,UAAU,GAAG,CAAC,WAAW,EAAE,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,gBAAgB,EAAE,cAAc,EAAE,uBAAuB,EAAE,MAAM,EAAE,wBAAwB,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,cAAc,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC;AACxR,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,UAAU,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,cAAc,IAAIC,KAAK,QAAQ,UAAU;AAC5G,OAAOC,IAAI,MAAM,4BAA4B;AAC7C,OAAOC,UAAU,MAAM,kCAAkC;AACzD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,IAAIC,qBAAqB,QAAQ,kBAAkB;AAChE,OAAOC,aAAa,IAAIC,qBAAqB,QAAQ,iBAAiB;AACtE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,SAASC,mBAAmBA,CAACC,GAAG,EAAE;EAChC,MAAMC,WAAW,GAAGD,GAAG,CAACE,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChD,OAAOF,WAAW,GAAGA,WAAW,CAACG,MAAM,GAAG,CAAC;AAC7C;AACA,SAASC,qBAAqBA,CAACC,KAAK,EAAEC,SAAS,EAAE;EAC/C,IAAID,KAAK,IAAI,IAAI,EAAE;IACjB,OAAOA,KAAK;EACd;EACA,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,KAAK,GAAGC,SAAS,CAAC,GAAGA,SAAS;EACzD,OAAOI,MAAM,CAACH,OAAO,CAACI,OAAO,CAACb,mBAAmB,CAACQ,SAAS,CAAC,CAAC,CAAC;AAChE;AACA,MAAMM,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,IAAI;IACJC,QAAQ;IACRC,QAAQ;IACRC,iBAAiB;IACjBC;EACF,CAAC,GAAGN,UAAU;EACd,MAAMO,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,OAAOxC,UAAU,CAACkC,IAAI,CAAC,EAAE,EAAEE,QAAQ,IAAI,UAAU,EAAEE,YAAY,IAAI,cAAc,EAAEH,QAAQ,IAAI,UAAU,CAAC;IACzHM,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;IAC5BC,eAAe,EAAE,CAACL,iBAAiB,IAAI,uBAAuB,CAAC;IAC/DM,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,YAAY,CAAC;IAC1BC,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,YAAY,CAAC;IAC1BC,OAAO,EAAE,CAAC,SAAS,CAAC;IACpBrD,cAAc,EAAE,CAAC,gBAAgB;EACnC,CAAC;EACD,OAAOE,cAAc,CAACyC,KAAK,EAAE3B,qBAAqB,EAAEqB,OAAO,CAAC;AAC9D,CAAC;AACD,MAAMiB,UAAU,GAAGzC,MAAM,CAAC,MAAM,EAAE;EAChC0C,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJvB;IACF,CAAC,GAAGsB,KAAK;IACT,OAAO,CAAC;MACN,CAAC,MAAM3C,aAAa,CAACf,cAAc,EAAE,GAAG2D,MAAM,CAAC3D;IACjD,CAAC,EAAE2D,MAAM,CAACf,IAAI,EAAEe,MAAM,CAAC,OAAOvD,UAAU,CAACgC,UAAU,CAACE,IAAI,CAAC,EAAE,CAAC,EAAEF,UAAU,CAACG,QAAQ,IAAIoB,MAAM,CAACpB,QAAQ,CAAC;EACvG;AACF,CAAC,CAAC,CAAC,CAAC;EACFqB,KAAK;EACLxB;AACF,CAAC,KAAK3C,QAAQ,CAAC;EACboE,OAAO,EAAE,aAAa;EACtB;EACAC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAEH,KAAK,CAACI,UAAU,CAACC,OAAO,CAAC,EAAE,CAAC;EACtCC,KAAK,EAAE,SAAS;EAChBC,MAAM,EAAE,SAAS;EACjBC,SAAS,EAAE,MAAM;EACjBC,KAAK,EAAE,aAAa;EACpBC,uBAAuB,EAAE,aAAa;EACtC,CAAC,KAAKvD,aAAa,CAACyB,QAAQ,EAAE,GAAG;IAC/B+B,OAAO,EAAE,CAACX,KAAK,CAACY,IAAI,IAAIZ,KAAK,EAAEa,OAAO,CAACC,MAAM,CAACC,eAAe;IAC7DC,aAAa,EAAE;EACjB,CAAC;EACD,CAAC,KAAK7D,aAAa,CAAC2B,YAAY,KAAK3B,aAAa,CAACqC,UAAU,EAAE,GAAG;IAChEyB,OAAO,EAAE;EACX,CAAC;EACD,CAAC,MAAM9D,aAAa,CAACf,cAAc,EAAE,GAAGA;AAC1C,CAAC,EAAEoC,UAAU,CAACE,IAAI,KAAK,OAAO,IAAI;EAChCyB,QAAQ,EAAEH,KAAK,CAACI,UAAU,CAACC,OAAO,CAAC,EAAE;AACvC,CAAC,EAAE7B,UAAU,CAACE,IAAI,KAAK,OAAO,IAAI;EAChCyB,QAAQ,EAAEH,KAAK,CAACI,UAAU,CAACC,OAAO,CAAC,EAAE;AACvC,CAAC,EAAE7B,UAAU,CAACG,QAAQ,IAAI;EACxBqC,aAAa,EAAE;AACjB,CAAC,CAAC,CAAC;AACH,MAAME,WAAW,GAAGjE,MAAM,CAAC,OAAO,EAAE;EAClC0C,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAAC;IAClBrB;EACF,CAAC,EAAEuB,MAAM,KAAK,CAACA,MAAM,CAACd,KAAK,EAAET,UAAU,CAACK,iBAAiB,IAAIkB,MAAM,CAACoB,qBAAqB;AAC3F,CAAC,CAAC,CAAC,CAAC;EACF3C;AACF,CAAC,KAAK3C,QAAQ,CAAC;EACb0E,MAAM,EAAE;AACV,CAAC,EAAE/B,UAAU,CAACK,iBAAiB,IAAI;EACjCuC,GAAG,EAAE,CAAC;EACNC,MAAM,EAAE,CAAC;EACTnB,QAAQ,EAAE,UAAU;EACpBe,OAAO,EAAE,gBAAgB;EACzBR,KAAK,EAAE;AACT,CAAC,CAAC,CAAC;AACH,MAAMa,UAAU,GAAGrE,MAAM,CAAC,MAAM,EAAE;EAChC0C,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJvB;IACF,CAAC,GAAGsB,KAAK;IACT,OAAO,CAACC,MAAM,CAACZ,IAAI,EAAEX,UAAU,CAACY,SAAS,IAAIW,MAAM,CAACX,SAAS,EAAEZ,UAAU,CAACa,UAAU,IAAIU,MAAM,CAACV,UAAU,EAAEb,UAAU,CAACc,SAAS,IAAIS,MAAM,CAACT,SAAS,EAAEd,UAAU,CAACe,SAAS,IAAIQ,MAAM,CAACR,SAAS,EAAEf,UAAU,CAACgB,UAAU,IAAIO,MAAM,CAACP,UAAU,CAAC;EAC5O;AACF,CAAC,CAAC,CAAC,CAAC;EACFQ,KAAK;EACLxB;AACF,CAAC,KAAK3C,QAAQ,CAAC;EACb;EACAoE,OAAO,EAAE,MAAM;EACfsB,UAAU,EAAEvB,KAAK,CAACwB,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;IAChDC,QAAQ,EAAE1B,KAAK,CAACwB,WAAW,CAACE,QAAQ,CAACC;EACvC,CAAC,CAAC;EACF;EACA;EACAX,aAAa,EAAE;AACjB,CAAC,EAAExC,UAAU,CAACgB,UAAU,IAAI;EAC1BoC,SAAS,EAAE;AACb,CAAC,EAAEpD,UAAU,CAACY,SAAS,IAAI;EACzBkB,KAAK,EAAE,CAACN,KAAK,CAACY,IAAI,IAAIZ,KAAK,EAAEa,OAAO,CAACC,MAAM,CAAClC;AAC9C,CAAC,CAAC,CAAC;AACH,MAAMiD,aAAa,GAAG5E,MAAM,CAAC,MAAM,EAAE;EACnC0C,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,SAAS;EACfkC,iBAAiB,EAAEC,IAAI,IAAI7E,qBAAqB,CAAC6E,IAAI,CAAC,IAAIA,IAAI,KAAK,YAAY;EAC/ElC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJP;IACF,CAAC,GAAGM,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,OAAO,EAAED,UAAU,IAAIO,MAAM,CAACP,UAAU,CAAC;EAC1D;AACF,CAAC,CAAC,CAAC,CAAC;EACFA;AACF,CAAC,KAAK3D,QAAQ,CAAC;EACbqE,QAAQ,EAAE;AACZ,CAAC,EAAEV,UAAU,IAAI;EACfoC,SAAS,EAAE;AACb,CAAC,CAAC,CAAC;AACH,SAASI,aAAaA,CAAClC,KAAK,EAAE;EAC5B,MAAMmC,KAAK,GAAGrG,6BAA6B,CAACkE,KAAK,EAAEhE,SAAS,CAAC;EAC7D,OAAO,aAAawB,IAAI,CAAC,MAAM,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAEoG,KAAK,CAAC,CAAC;AACvD;AACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGJ,aAAa,CAACK,SAAS,GAAG;EAChErE,KAAK,EAAE/B,SAAS,CAACqG,MAAM,CAACC;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,SAASC,UAAUA,CAAC1C,KAAK,EAAE;EACzB,MAAM;IACJrB,OAAO;IACPG,QAAQ;IACR6D,SAAS;IACTC,KAAK;IACLC,YAAY;IACZC,qBAAqB;IACrBC,KAAK;IACL1D,IAAI;IACJ2D,sBAAsB;IACtBC,QAAQ;IACRC,SAAS;IACTC,UAAU;IACVtD,IAAI;IACJuD,MAAM;IACNC,QAAQ;IACRC,OAAO;IACPC,OAAO;IACP1E,QAAQ;IACRH,UAAU;IACV8E,WAAW;IACXC;EACF,CAAC,GAAGzD,KAAK;EACT,MAAM0D,QAAQ,GAAGZ,qBAAqB,GAAGI,SAAS,KAAKM,WAAW,GAAGN,SAAS,IAAIM,WAAW;EAC7F,MAAMG,SAAS,GAAGT,SAAS,IAAIH,KAAK;EACpC,MAAMa,SAAS,GAAGV,SAAS,IAAIN,KAAK;EACpC,MAAMiB,SAAS,GAAGX,SAAS,KAAKO,kBAAkB;EAClD,MAAMK,EAAE,GAAG/G,KAAK,CAAC,CAAC;EAClB,MAAMgH,SAAS,GAAG,aAAavG,IAAI,CAACgE,UAAU,EAAE;IAC9CwC,EAAE,EAAEhB,sBAAsB;IAC1B9E,KAAK,EAAEgF,SAAS;IAChBe,SAAS,EAAE7H,IAAI,CAACuC,OAAO,CAACU,IAAI,EAAEqE,QAAQ,GAAG/E,OAAO,CAACY,UAAU,GAAGZ,OAAO,CAACW,SAAS,EAAEqE,SAAS,IAAIhF,OAAO,CAACa,SAAS,EAAEoE,SAAS,IAAIjF,OAAO,CAACc,SAAS,EAAEwD,QAAQ,IAAItE,OAAO,CAACe,UAAU,CAAC;IAChLhB,UAAU,EAAE3C,QAAQ,CAAC,CAAC,CAAC,EAAE2C,UAAU,EAAE;MACnCY,SAAS,EAAE,CAACoE,QAAQ;MACpBnE,UAAU,EAAEmE,QAAQ;MACpBlE,SAAS,EAAEmE,SAAS;MACpBlE,SAAS,EAAEmE,SAAS;MACpBlE,UAAU,EAAEuD;IACd,CAAC,CAAC;IACFiB,QAAQ,EAAEvB,SAAS,IAAI,CAACe,QAAQ,GAAGf,SAAS,GAAGtD;EACjD,CAAC,CAAC;EACF,IAAIR,QAAQ,EAAE;IACZ,OAAO,aAAarB,IAAI,CAAC,MAAM,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAEoH,UAAU,EAAE;MACxDe,QAAQ,EAAEH;IACZ,CAAC,CAAC,CAAC;EACL;EACA,OAAO,aAAarG,KAAK,CAACxB,KAAK,CAACiI,QAAQ,EAAE;IACxCD,QAAQ,EAAE,CAAC,aAAaxG,KAAK,CAAC0D,WAAW,EAAErF,QAAQ,CAAC;MAClD2C,UAAU,EAAE3C,QAAQ,CAAC,CAAC,CAAC,EAAE2C,UAAU,EAAE;QACnCK,iBAAiB,EAAEqF;MACrB,CAAC,CAAC;MACFC,OAAO,EAAEP;IACX,CAAC,EAAEX,UAAU,EAAE;MACbe,QAAQ,EAAE,CAACH,SAAS,EAAE,aAAavG,IAAI,CAAC,MAAM,EAAE;QAC9CyG,SAAS,EAAEtF,OAAO,CAACrC,cAAc;QACjC4H,QAAQ,EAAErB,YAAY,CAACK,SAAS;MAClC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,EAAE,aAAa1F,IAAI,CAAC,OAAO,EAAE;MAC9ByG,SAAS,EAAEtF,OAAO,CAACrC,cAAc;MACjCiH,OAAO,EAAEA,OAAO;MAChBH,MAAM,EAAEA,MAAM;MACdC,QAAQ,EAAEA,QAAQ;MAClBC,OAAO,EAAEA,OAAO;MAChBxE,QAAQ,EAAEA,QAAQ;MAClBZ,KAAK,EAAEgF,SAAS;MAChBY,EAAE,EAAEA,EAAE;MACNQ,IAAI,EAAE,OAAO;MACbzE,IAAI,EAAEA,IAAI;MACV0E,OAAO,EAAEV;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACAzB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGI,UAAU,CAACH,SAAS,GAAG;EAC7D5D,OAAO,EAAExC,SAAS,CAACqI,MAAM,CAAC/B,UAAU;EACpC3D,QAAQ,EAAE3C,SAAS,CAACsI,IAAI,CAAChC,UAAU;EACnCE,SAAS,EAAExG,SAAS,CAACuI,IAAI;EACzB9B,KAAK,EAAEzG,SAAS,CAACqG,MAAM,CAACC,UAAU;EAClCI,YAAY,EAAE1G,SAAS,CAACwI,IAAI,CAAClC,UAAU;EACvCK,qBAAqB,EAAE3G,SAAS,CAACsI,IAAI,CAAChC,UAAU;EAChDM,KAAK,EAAE5G,SAAS,CAACqG,MAAM,CAACC,UAAU;EAClCpD,IAAI,EAAElD,SAAS,CAACuI,IAAI;EACpB1B,sBAAsB,EAAE7G,SAAS,CAACyI,WAAW,CAACnC,UAAU;EACxDQ,QAAQ,EAAE9G,SAAS,CAACsI,IAAI,CAAChC,UAAU;EACnCS,SAAS,EAAE/G,SAAS,CAACqG,MAAM,CAACC,UAAU;EACtCU,UAAU,EAAEhH,SAAS,CAACqI,MAAM;EAC5B3E,IAAI,EAAE1D,SAAS,CAAC0I,MAAM;EACtBzB,MAAM,EAAEjH,SAAS,CAACwI,IAAI,CAAClC,UAAU;EACjCY,QAAQ,EAAElH,SAAS,CAACwI,IAAI,CAAClC,UAAU;EACnCa,OAAO,EAAEnH,SAAS,CAACwI,IAAI,CAAClC,UAAU;EAClCc,OAAO,EAAEpH,SAAS,CAACwI,IAAI,CAAClC,UAAU;EAClC/D,UAAU,EAAEvC,SAAS,CAACqI,MAAM,CAAC/B,UAAU;EACvCe,WAAW,EAAErH,SAAS,CAACqG,MAAM;EAC7BiB,kBAAkB,EAAEtH,SAAS,CAACqG,MAAM;EACpC3D,QAAQ,EAAE1C,SAAS,CAACsI,IAAI,CAAChC;AAC3B,CAAC,GAAG,KAAK,CAAC;AACV,MAAMqC,WAAW,GAAG,aAAatH,IAAI,CAACR,IAAI,EAAE;EAC1CqD,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,MAAM0E,gBAAgB,GAAG,aAAavH,IAAI,CAACP,UAAU,EAAE;EACrDoD,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,SAAS2E,gBAAgBA,CAAC9G,KAAK,EAAE;EAC/B,OAAO,GAAGA,KAAK,QAAQA,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;AACjD;AACA,MAAM+G,MAAM,GAAG,aAAa/I,KAAK,CAACgJ,UAAU,CAAC,SAASD,MAAMA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzE,MAAMpF,KAAK,GAAG9C,eAAe,CAAC;IAC5B2C,IAAI,EAAE,WAAW;IACjBG,KAAK,EAAEmF;EACT,CAAC,CAAC;EACF,MAAM;MACFlB,SAAS;MACToB,YAAY,GAAG,IAAI;MACnBvG,QAAQ,GAAG,KAAK;MAChB6D,SAAS,GAAGoC,gBAAgB;MAC5BO,cAAc,GAAG,OAAO;MACxBzC,YAAY,GAAGmC,gBAAgB;MAC/BlC,qBAAqB,GAAG,KAAK;MAC7BzD,IAAI,GAAGyF,WAAW;MAClB9B,sBAAsB,GAAGd,aAAa;MACtCqD,GAAG,GAAG,CAAC;MACP1F,IAAI,EAAE2F,QAAQ;MACdnC,QAAQ;MACRoC,cAAc;MACdC,YAAY;MACZC,WAAW;MACXxH,SAAS,GAAG,CAAC;MACbU,QAAQ,GAAG,KAAK;MAChBD,IAAI,GAAG,QAAQ;MACfV,KAAK,EAAE0H;IACT,CAAC,GAAG5F,KAAK;IACTmC,KAAK,GAAGrG,6BAA6B,CAACkE,KAAK,EAAE/D,UAAU,CAAC;EAC1D,MAAM4D,IAAI,GAAG9C,KAAK,CAACyI,QAAQ,CAAC;EAC5B,MAAM,CAACK,YAAY,EAAEC,aAAa,CAAC,GAAGjJ,aAAa,CAAC;IAClDkJ,UAAU,EAAEH,SAAS;IACrBI,OAAO,EAAEX,YAAY;IACrBxF,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMoG,YAAY,GAAGhI,qBAAqB,CAAC4H,YAAY,EAAE1H,SAAS,CAAC;EACnE,MAAM+H,KAAK,GAAGzJ,MAAM,CAAC,CAAC;EACtB,MAAM,CAAC;IACLsG,KAAK;IACLH;EACF,CAAC,EAAEuD,QAAQ,CAAC,GAAGjK,KAAK,CAACkK,QAAQ,CAAC;IAC5BrD,KAAK,EAAE,CAAC,CAAC;IACTH,KAAK,EAAE,CAAC;EACV,CAAC,CAAC;EACF,IAAI1E,KAAK,GAAG+H,YAAY;EACxB,IAAIlD,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB7E,KAAK,GAAG6E,KAAK;EACf;EACA,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB1E,KAAK,GAAG0E,KAAK;EACf;EACA,MAAM;IACJyD,iBAAiB;IACjBjD,MAAM,EAAEkD,iBAAiB;IACzB/C,OAAO,EAAEgD,kBAAkB;IAC3BnB,GAAG,EAAEoB;EACP,CAAC,GAAG5J,iBAAiB,CAAC,CAAC;EACvB,MAAM,CAACoC,YAAY,EAAEyH,eAAe,CAAC,GAAGvK,KAAK,CAACkK,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMM,OAAO,GAAGxK,KAAK,CAACyK,MAAM,CAAC,CAAC;EAC9B,MAAMC,SAAS,GAAGjK,UAAU,CAAC6J,eAAe,EAAEE,OAAO,EAAEtB,GAAG,CAAC;EAC3D,MAAMyB,eAAe,GAAGC,KAAK,IAAI;IAC/B,IAAInB,WAAW,EAAE;MACfA,WAAW,CAACmB,KAAK,CAAC;IACpB;IACA,MAAMC,QAAQ,GAAGL,OAAO,CAACM,OAAO;IAChC,MAAM;MACJC,KAAK;MACLC,IAAI;MACJvG,KAAK,EAAEwG;IACT,CAAC,GAAGJ,QAAQ,CAACK,qBAAqB,CAAC,CAAC;IACpC,IAAIC,OAAO;IACX,IAAInB,KAAK,EAAE;MACTmB,OAAO,GAAG,CAACJ,KAAK,GAAGH,KAAK,CAACQ,OAAO,IAAIH,cAAc;IACpD,CAAC,MAAM;MACLE,OAAO,GAAG,CAACP,KAAK,CAACQ,OAAO,GAAGJ,IAAI,IAAIC,cAAc;IACnD;IACA,IAAII,QAAQ,GAAGtJ,qBAAqB,CAACsH,GAAG,GAAG8B,OAAO,GAAGlJ,SAAS,GAAG,CAAC,EAAEA,SAAS,CAAC;IAC9EoJ,QAAQ,GAAGlL,KAAK,CAACkL,QAAQ,EAAEpJ,SAAS,EAAEoH,GAAG,CAAC;IAC1CY,QAAQ,CAACqB,IAAI,IAAIA,IAAI,CAACzE,KAAK,KAAKwE,QAAQ,IAAIC,IAAI,CAAC5E,KAAK,KAAK2E,QAAQ,GAAGC,IAAI,GAAG;MAC3EzE,KAAK,EAAEwE,QAAQ;MACf3E,KAAK,EAAE2E;IACT,CAAC,CAAC;IACFd,eAAe,CAAC,KAAK,CAAC;IACtB,IAAIhB,cAAc,IAAI1C,KAAK,KAAKwE,QAAQ,EAAE;MACxC9B,cAAc,CAACqB,KAAK,EAAES,QAAQ,CAAC;IACjC;EACF,CAAC;EACD,MAAME,gBAAgB,GAAGX,KAAK,IAAI;IAChC,IAAIpB,YAAY,EAAE;MAChBA,YAAY,CAACoB,KAAK,CAAC;IACrB;IACA,MAAMS,QAAQ,GAAG,CAAC,CAAC;IACnBpB,QAAQ,CAAC;MACPpD,KAAK,EAAEwE,QAAQ;MACf3E,KAAK,EAAE2E;IACT,CAAC,CAAC;IACF,IAAI9B,cAAc,IAAI1C,KAAK,KAAKwE,QAAQ,EAAE;MACxC9B,cAAc,CAACqB,KAAK,EAAES,QAAQ,CAAC;IACjC;EACF,CAAC;EACD,MAAMG,YAAY,GAAGZ,KAAK,IAAI;IAC5B,IAAIa,QAAQ,GAAGb,KAAK,CAACc,MAAM,CAAC1J,KAAK,KAAK,EAAE,GAAG,IAAI,GAAG2J,UAAU,CAACf,KAAK,CAACc,MAAM,CAAC1J,KAAK,CAAC;;IAEhF;IACA;IACA,IAAI6E,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB4E,QAAQ,GAAG5E,KAAK;IAClB;IACA+C,aAAa,CAAC6B,QAAQ,CAAC;IACvB,IAAItE,QAAQ,EAAE;MACZA,QAAQ,CAACyD,KAAK,EAAEa,QAAQ,CAAC;IAC3B;EACF,CAAC;EACD,MAAMG,WAAW,GAAGhB,KAAK,IAAI;IAC3B;IACA;IACA,IAAIA,KAAK,CAACQ,OAAO,KAAK,CAAC,IAAIR,KAAK,CAACiB,OAAO,KAAK,CAAC,EAAE;MAC9C;IACF;IACA5B,QAAQ,CAAC;MACPpD,KAAK,EAAE,CAAC,CAAC;MACTH,KAAK,EAAE,CAAC;IACV,CAAC,CAAC;IACFkD,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIzC,QAAQ,IAAIwE,UAAU,CAACf,KAAK,CAACc,MAAM,CAAC1J,KAAK,CAAC,KAAK+H,YAAY,EAAE;MAC/D5C,QAAQ,CAACyD,KAAK,EAAE,IAAI,CAAC;IACvB;EACF,CAAC;EACD,MAAMkB,WAAW,GAAGlB,KAAK,IAAI;IAC3BP,kBAAkB,CAACO,KAAK,CAAC;IACzB,IAAIT,iBAAiB,CAACW,OAAO,KAAK,IAAI,EAAE;MACtCP,eAAe,CAAC,IAAI,CAAC;IACvB;IACA,MAAMwB,QAAQ,GAAGJ,UAAU,CAACf,KAAK,CAACc,MAAM,CAAC1J,KAAK,CAAC;IAC/CiI,QAAQ,CAACqB,IAAI,KAAK;MAChBzE,KAAK,EAAEyE,IAAI,CAACzE,KAAK;MACjBH,KAAK,EAAEqF;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EACD,MAAMC,UAAU,GAAGpB,KAAK,IAAI;IAC1B,IAAI/D,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB;IACF;IACAuD,iBAAiB,CAACQ,KAAK,CAAC;IACxB,IAAIT,iBAAiB,CAACW,OAAO,KAAK,KAAK,EAAE;MACvCP,eAAe,CAAC,KAAK,CAAC;IACxB;IACA,MAAMwB,QAAQ,GAAG,CAAC,CAAC;IACnB9B,QAAQ,CAACqB,IAAI,KAAK;MAChBzE,KAAK,EAAEyE,IAAI,CAACzE,KAAK;MACjBH,KAAK,EAAEqF;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EACD,MAAM,CAAClJ,iBAAiB,EAAEoJ,oBAAoB,CAAC,GAAGjM,KAAK,CAACkK,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM1H,UAAU,GAAG3C,QAAQ,CAAC,CAAC,CAAC,EAAEiE,KAAK,EAAE;IACrCqF,YAAY;IACZvG,QAAQ;IACR6D,SAAS;IACT2C,cAAc;IACdvG,iBAAiB;IACjBC,YAAY;IACZ6D,YAAY;IACZxD,IAAI;IACJ2D,sBAAsB;IACtBuC,GAAG;IACHpH,SAAS;IACTU,QAAQ;IACRD;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAahB,KAAK,CAACkC,UAAU,EAAE7D,QAAQ,CAAC;IAC7CqJ,GAAG,EAAEwB,SAAS;IACdjB,WAAW,EAAEkB,eAAe;IAC5BnB,YAAY,EAAE+B,gBAAgB;IAC9BxD,SAAS,EAAE7H,IAAI,CAACuC,OAAO,CAACO,IAAI,EAAE+E,SAAS,EAAEpF,QAAQ,IAAI,oBAAoB,CAAC;IAC1EH,UAAU,EAAEA,UAAU;IACtB0J,IAAI,EAAEvJ,QAAQ,GAAG,KAAK,GAAG,IAAI;IAC7B,YAAY,EAAEA,QAAQ,GAAGgE,YAAY,CAAC3E,KAAK,CAAC,GAAG;EACjD,CAAC,EAAEiE,KAAK,EAAE;IACR+B,QAAQ,EAAE,CAACmE,KAAK,CAACC,IAAI,CAAC,IAAID,KAAK,CAAC9C,GAAG,CAAC,CAAC,CAACgD,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;MACtD,MAAMvF,SAAS,GAAGuF,KAAK,GAAG,CAAC;MAC3B,MAAMC,eAAe,GAAG;QACtB/J,OAAO;QACPG,QAAQ;QACR6D,SAAS;QACTC,KAAK;QACLC,YAAY;QACZC,qBAAqB;QACrBC,KAAK;QACL1D,IAAI;QACJ2D,sBAAsB;QACtBnD,IAAI;QACJuD,MAAM,EAAE8E,UAAU;QAClB7E,QAAQ,EAAEqE,YAAY;QACtBpE,OAAO,EAAEwE,WAAW;QACpBvE,OAAO,EAAEyE,WAAW;QACpBxE,WAAW,EAAEtF,KAAK;QAClBuF,kBAAkB,EAAEwC,YAAY;QAChCpH,QAAQ;QACRH;MACF,CAAC;MACD,MAAMuE,QAAQ,GAAGC,SAAS,KAAK7E,IAAI,CAACsK,IAAI,CAACzK,KAAK,CAAC,KAAK6E,KAAK,KAAK,CAAC,CAAC,IAAIH,KAAK,KAAK,CAAC,CAAC,CAAC;MACjF,IAAIzE,SAAS,GAAG,CAAC,EAAE;QACjB,MAAMyK,KAAK,GAAGP,KAAK,CAACC,IAAI,CAAC,IAAID,KAAK,CAAC,CAAC,GAAGlK,SAAS,CAAC,CAAC;QAClD,OAAO,aAAaX,IAAI,CAACuE,aAAa,EAAE;UACtCkC,SAAS,EAAE7H,IAAI,CAACuC,OAAO,CAACgB,OAAO,EAAEsD,QAAQ,IAAItE,OAAO,CAACe,UAAU,CAAC;UAChEhB,UAAU,EAAEA,UAAU;UACtBgB,UAAU,EAAEuD,QAAQ;UACpBiB,QAAQ,EAAE0E,KAAK,CAACL,GAAG,CAAC,CAACM,CAAC,EAAEC,YAAY,KAAK;YACvC,MAAMC,gBAAgB,GAAG9K,qBAAqB,CAACiF,SAAS,GAAG,CAAC,GAAG,CAAC4F,YAAY,GAAG,CAAC,IAAI3K,SAAS,EAAEA,SAAS,CAAC;YACzG,OAAO,aAAaX,IAAI,CAACkF,UAAU,EAAE3G,QAAQ,CAAC,CAAC,CAAC,EAAE2M,eAAe,EAAE;cACjE;cACAzF,QAAQ,EAAE,KAAK;cACfC,SAAS,EAAE6F,gBAAgB;cAC3B5F,UAAU,EAAE;gBACV6F,KAAK,EAAEJ,KAAK,CAAC5K,MAAM,GAAG,CAAC,KAAK8K,YAAY,GAAG,CAAC,CAAC,GAAG;kBAC9CnI,KAAK,EAAEoI,gBAAgB,KAAK7K,KAAK,GAAG,GAAG,CAAC4K,YAAY,GAAG,CAAC,IAAI3K,SAAS,GAAG,GAAG,GAAG,GAAG,IAAI;kBACrF8K,QAAQ,EAAE,QAAQ;kBAClB7I,QAAQ,EAAE;gBACZ;cACF;YACF,CAAC,CAAC,EAAE2I,gBAAgB,CAAC;UACvB,CAAC;QACH,CAAC,EAAE7F,SAAS,CAAC;MACf;MACA,OAAO,aAAa1F,IAAI,CAACkF,UAAU,EAAE3G,QAAQ,CAAC,CAAC,CAAC,EAAE2M,eAAe,EAAE;QACjEzF,QAAQ,EAAEA,QAAQ;QAClBC,SAAS,EAAEA;MACb,CAAC,CAAC,EAAEA,SAAS,CAAC;IAChB,CAAC,CAAC,EAAE,CAACrE,QAAQ,IAAI,CAACC,QAAQ,IAAI,aAAapB,KAAK,CAAC0D,WAAW,EAAE;MAC5D6C,SAAS,EAAE7H,IAAI,CAACuC,OAAO,CAACQ,KAAK,EAAER,OAAO,CAACS,eAAe,CAAC;MACvDV,UAAU,EAAEA,UAAU;MACtBwF,QAAQ,EAAE,CAAC,aAAa1G,IAAI,CAAC,OAAO,EAAE;QACpCyG,SAAS,EAAEtF,OAAO,CAACrC,cAAc;QACjC4B,KAAK,EAAE,EAAE;QACT4F,EAAE,EAAE,GAAGjE,IAAI,QAAQ;QACnByE,IAAI,EAAE,OAAO;QACbzE,IAAI,EAAEA,IAAI;QACV0E,OAAO,EAAE0B,YAAY,IAAI,IAAI;QAC7B1C,OAAO,EAAEA,CAAA,KAAM4E,oBAAoB,CAAC,IAAI,CAAC;QACzC/E,MAAM,EAAEA,CAAA,KAAM+E,oBAAoB,CAAC,KAAK,CAAC;QACzC9E,QAAQ,EAAEqE;MACZ,CAAC,CAAC,EAAE,aAAalK,IAAI,CAAC,MAAM,EAAE;QAC5ByG,SAAS,EAAEtF,OAAO,CAACrC,cAAc;QACjC4H,QAAQ,EAAEoB;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFlD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG2C,MAAM,CAAC1C,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;EACE5D,OAAO,EAAExC,SAAS,CAACqI,MAAM;EACzB;AACF;AACA;EACEP,SAAS,EAAE9H,SAAS,CAAC0I,MAAM;EAC3B;AACF;AACA;AACA;EACEQ,YAAY,EAAElJ,SAAS,CAACqG,MAAM;EAC9B;AACF;AACA;AACA;EACE1D,QAAQ,EAAE3C,SAAS,CAACsI,IAAI;EACxB;AACF;AACA;AACA;EACE9B,SAAS,EAAExG,SAAS,CAACuI,IAAI;EACzB;AACF;AACA;AACA;EACEY,cAAc,EAAEnJ,SAAS,CAACuI,IAAI;EAC9B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE7B,YAAY,EAAE1G,SAAS,CAACwI,IAAI;EAC5B;AACF;AACA;AACA;EACE7B,qBAAqB,EAAE3G,SAAS,CAACsI,IAAI;EACrC;AACF;AACA;AACA;EACEpF,IAAI,EAAElD,SAAS,CAACuI,IAAI;EACpB;AACF;AACA;AACA;AACA;AACA;AACA;EACE1B,sBAAsB,EAAE7G,SAAS,CAACyI,WAAW;EAC7C;AACF;AACA;AACA;EACEW,GAAG,EAAEpJ,SAAS,CAACqG,MAAM;EACrB;AACF;AACA;AACA;AACA;EACE3C,IAAI,EAAE1D,SAAS,CAAC0I,MAAM;EACtB;AACF;AACA;AACA;AACA;EACExB,QAAQ,EAAElH,SAAS,CAACwI,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEc,cAAc,EAAEtJ,SAAS,CAACwI,IAAI;EAC9B;AACF;AACA;EACEe,YAAY,EAAEvJ,SAAS,CAACwI,IAAI;EAC5B;AACF;AACA;EACEgB,WAAW,EAAExJ,SAAS,CAACwI,IAAI;EAC3B;AACF;AACA;AACA;EACExG,SAAS,EAAE5B,cAAc,CAACJ,SAAS,CAACqG,MAAM,EAAExC,KAAK,IAAI;IACnD,IAAIA,KAAK,CAAC7B,SAAS,GAAG,GAAG,EAAE;MACzB,OAAO,IAAI+K,KAAK,CAAC,CAAC,gDAAgD,EAAE,uDAAuD,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1I;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEtK,QAAQ,EAAE1C,SAAS,CAACsI,IAAI;EACxB;AACF;AACA;AACA;EACE7F,IAAI,EAAEzC,SAAS,CAAC,sCAAsCiN,SAAS,CAAC,CAACjN,SAAS,CAACkN,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAElN,SAAS,CAAC0I,MAAM,CAAC,CAAC;EAClI;AACF;AACA;EACEyE,EAAE,EAAEnN,SAAS,CAACiN,SAAS,CAAC,CAACjN,SAAS,CAACoN,OAAO,CAACpN,SAAS,CAACiN,SAAS,CAAC,CAACjN,SAAS,CAACwI,IAAI,EAAExI,SAAS,CAACqI,MAAM,EAAErI,SAAS,CAACsI,IAAI,CAAC,CAAC,CAAC,EAAEtI,SAAS,CAACwI,IAAI,EAAExI,SAAS,CAACqI,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACEtG,KAAK,EAAE/B,SAAS,CAACqG;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAeyC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}