{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"baseClassName\", \"className\", \"color\", \"component\", \"fontSize\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport capitalize from '../utils/capitalize';\nimport { getIconUtilityClass } from './iconClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    fontSize,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', color !== 'inherit' && `color${capitalize(color)}`, `fontSize${capitalize(fontSize)}`]\n  };\n  return composeClasses(slots, getIconUtilityClass, classes);\n};\nconst IconRoot = styled('span', {\n  name: 'MuiIcon',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.color !== 'inherit' && styles[`color${capitalize(ownerState.color)}`], styles[`fontSize${capitalize(ownerState.fontSize)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => ({\n  userSelect: 'none',\n  width: '1em',\n  height: '1em',\n  // Chrome fix for https://bugs.chromium.org/p/chromium/issues/detail?id=820541\n  // To remove at some point.\n  overflow: 'hidden',\n  display: 'inline-block',\n  // allow overflow hidden to take action\n  textAlign: 'center',\n  // support non-square icon\n  flexShrink: 0,\n  fontSize: {\n    inherit: 'inherit',\n    small: theme.typography.pxToRem(20),\n    medium: theme.typography.pxToRem(24),\n    large: theme.typography.pxToRem(36)\n  }[ownerState.fontSize],\n  // TODO v5 deprecate, v6 remove for sx\n  color: {\n    primary: (theme.vars || theme).palette.primary.main,\n    secondary: (theme.vars || theme).palette.secondary.main,\n    info: (theme.vars || theme).palette.info.main,\n    success: (theme.vars || theme).palette.success.main,\n    warning: (theme.vars || theme).palette.warning.main,\n    action: (theme.vars || theme).palette.action.active,\n    error: (theme.vars || theme).palette.error.main,\n    disabled: (theme.vars || theme).palette.action.disabled,\n    inherit: undefined\n  }[ownerState.color]\n}));\nconst Icon = /*#__PURE__*/React.forwardRef(function Icon(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiIcon'\n  });\n  const {\n      baseClassName = 'material-icons',\n      className,\n      color = 'inherit',\n      component: Component = 'span',\n      fontSize = 'medium'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    baseClassName,\n    color,\n    component: Component,\n    fontSize\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(IconRoot, _extends({\n    as: Component,\n    className: clsx(baseClassName,\n    // Prevent the translation of the text content.\n    // The font relies on the exact text content to render the icon.\n    'notranslate', classes.root, className),\n    ownerState: ownerState,\n    \"aria-hidden\": true,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Icon.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The base class applied to the icon. Defaults to 'material-icons', but can be changed to any\n   * other base class that suits the icon font you're using (for example material-icons-rounded, fas, etc).\n   * @default 'material-icons'\n   */\n  baseClassName: PropTypes.string,\n  /**\n   * The name of the icon font ligature.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'inherit'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['inherit', 'action', 'disabled', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The fontSize applied to the icon. Defaults to 24px, but can be configure to inherit font size.\n   * @default 'medium'\n   */\n  fontSize: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['inherit', 'large', 'medium', 'small']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nIcon.muiName = 'Icon';\nexport default Icon;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "capitalize", "getIconUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "color", "fontSize", "classes", "slots", "root", "IconRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "userSelect", "width", "height", "overflow", "display", "textAlign", "flexShrink", "inherit", "small", "typography", "pxToRem", "medium", "large", "primary", "vars", "palette", "main", "secondary", "info", "success", "warning", "action", "active", "error", "disabled", "undefined", "Icon", "forwardRef", "inProps", "ref", "baseClassName", "className", "component", "Component", "other", "as", "process", "env", "NODE_ENV", "propTypes", "string", "children", "node", "object", "oneOfType", "oneOf", "elementType", "sx", "arrayOf", "func", "bool", "mui<PERSON><PERSON>"], "sources": ["D:/Safety Tracking Web App/node_modules/@mui/material/Icon/Icon.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"baseClassName\", \"className\", \"color\", \"component\", \"fontSize\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport capitalize from '../utils/capitalize';\nimport { getIconUtilityClass } from './iconClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    fontSize,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', color !== 'inherit' && `color${capitalize(color)}`, `fontSize${capitalize(fontSize)}`]\n  };\n  return composeClasses(slots, getIconUtilityClass, classes);\n};\nconst IconRoot = styled('span', {\n  name: 'MuiIcon',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.color !== 'inherit' && styles[`color${capitalize(ownerState.color)}`], styles[`fontSize${capitalize(ownerState.fontSize)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => ({\n  userSelect: 'none',\n  width: '1em',\n  height: '1em',\n  // Chrome fix for https://bugs.chromium.org/p/chromium/issues/detail?id=820541\n  // To remove at some point.\n  overflow: 'hidden',\n  display: 'inline-block',\n  // allow overflow hidden to take action\n  textAlign: 'center',\n  // support non-square icon\n  flexShrink: 0,\n  fontSize: {\n    inherit: 'inherit',\n    small: theme.typography.pxToRem(20),\n    medium: theme.typography.pxToRem(24),\n    large: theme.typography.pxToRem(36)\n  }[ownerState.fontSize],\n  // TODO v5 deprecate, v6 remove for sx\n  color: {\n    primary: (theme.vars || theme).palette.primary.main,\n    secondary: (theme.vars || theme).palette.secondary.main,\n    info: (theme.vars || theme).palette.info.main,\n    success: (theme.vars || theme).palette.success.main,\n    warning: (theme.vars || theme).palette.warning.main,\n    action: (theme.vars || theme).palette.action.active,\n    error: (theme.vars || theme).palette.error.main,\n    disabled: (theme.vars || theme).palette.action.disabled,\n    inherit: undefined\n  }[ownerState.color]\n}));\nconst Icon = /*#__PURE__*/React.forwardRef(function Icon(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiIcon'\n  });\n  const {\n      baseClassName = 'material-icons',\n      className,\n      color = 'inherit',\n      component: Component = 'span',\n      fontSize = 'medium'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    baseClassName,\n    color,\n    component: Component,\n    fontSize\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(IconRoot, _extends({\n    as: Component,\n    className: clsx(baseClassName,\n    // Prevent the translation of the text content.\n    // The font relies on the exact text content to render the icon.\n    'notranslate', classes.root, className),\n    ownerState: ownerState,\n    \"aria-hidden\": true,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Icon.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The base class applied to the icon. Defaults to 'material-icons', but can be changed to any\n   * other base class that suits the icon font you're using (for example material-icons-rounded, fas, etc).\n   * @default 'material-icons'\n   */\n  baseClassName: PropTypes.string,\n  /**\n   * The name of the icon font ligature.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'inherit'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['inherit', 'action', 'disabled', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The fontSize applied to the icon. Defaults to 24px, but can be configure to inherit font size.\n   * @default 'medium'\n   */\n  fontSize: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['inherit', 'large', 'medium', 'small']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nIcon.muiName = 'Icon';\nexport default Icon;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,eAAe,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC;AAClF,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,SAASC,mBAAmB,QAAQ,eAAe;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,KAAK;IACLC,QAAQ;IACRC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEJ,KAAK,KAAK,SAAS,IAAI,QAAQN,UAAU,CAACM,KAAK,CAAC,EAAE,EAAE,WAAWN,UAAU,CAACO,QAAQ,CAAC,EAAE;EACtG,CAAC;EACD,OAAOV,cAAc,CAACY,KAAK,EAAER,mBAAmB,EAAEO,OAAO,CAAC;AAC5D,CAAC;AACD,MAAMG,QAAQ,GAAGb,MAAM,CAAC,MAAM,EAAE;EAC9Bc,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJX;IACF,CAAC,GAAGU,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEL,UAAU,CAACC,KAAK,KAAK,SAAS,IAAIU,MAAM,CAAC,QAAQhB,UAAU,CAACK,UAAU,CAACC,KAAK,CAAC,EAAE,CAAC,EAAEU,MAAM,CAAC,WAAWhB,UAAU,CAACK,UAAU,CAACE,QAAQ,CAAC,EAAE,CAAC,CAAC;EAC9J;AACF,CAAC,CAAC,CAAC,CAAC;EACFU,KAAK;EACLZ;AACF,CAAC,MAAM;EACLa,UAAU,EAAE,MAAM;EAClBC,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,KAAK;EACb;EACA;EACAC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,cAAc;EACvB;EACAC,SAAS,EAAE,QAAQ;EACnB;EACAC,UAAU,EAAE,CAAC;EACbjB,QAAQ,EAAE;IACRkB,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAET,KAAK,CAACU,UAAU,CAACC,OAAO,CAAC,EAAE,CAAC;IACnCC,MAAM,EAAEZ,KAAK,CAACU,UAAU,CAACC,OAAO,CAAC,EAAE,CAAC;IACpCE,KAAK,EAAEb,KAAK,CAACU,UAAU,CAACC,OAAO,CAAC,EAAE;EACpC,CAAC,CAACvB,UAAU,CAACE,QAAQ,CAAC;EACtB;EACAD,KAAK,EAAE;IACLyB,OAAO,EAAE,CAACd,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEgB,OAAO,CAACF,OAAO,CAACG,IAAI;IACnDC,SAAS,EAAE,CAAClB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEgB,OAAO,CAACE,SAAS,CAACD,IAAI;IACvDE,IAAI,EAAE,CAACnB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEgB,OAAO,CAACG,IAAI,CAACF,IAAI;IAC7CG,OAAO,EAAE,CAACpB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEgB,OAAO,CAACI,OAAO,CAACH,IAAI;IACnDI,OAAO,EAAE,CAACrB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEgB,OAAO,CAACK,OAAO,CAACJ,IAAI;IACnDK,MAAM,EAAE,CAACtB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEgB,OAAO,CAACM,MAAM,CAACC,MAAM;IACnDC,KAAK,EAAE,CAACxB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEgB,OAAO,CAACQ,KAAK,CAACP,IAAI;IAC/CQ,QAAQ,EAAE,CAACzB,KAAK,CAACe,IAAI,IAAIf,KAAK,EAAEgB,OAAO,CAACM,MAAM,CAACG,QAAQ;IACvDjB,OAAO,EAAEkB;EACX,CAAC,CAACtC,UAAU,CAACC,KAAK;AACpB,CAAC,CAAC,CAAC;AACH,MAAMsC,IAAI,GAAG,aAAalD,KAAK,CAACmD,UAAU,CAAC,SAASD,IAAIA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrE,MAAMhC,KAAK,GAAGhB,eAAe,CAAC;IAC5BgB,KAAK,EAAE+B,OAAO;IACdlC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFoC,aAAa,GAAG,gBAAgB;MAChCC,SAAS;MACT3C,KAAK,GAAG,SAAS;MACjB4C,SAAS,EAAEC,SAAS,GAAG,MAAM;MAC7B5C,QAAQ,GAAG;IACb,CAAC,GAAGQ,KAAK;IACTqC,KAAK,GAAG5D,6BAA6B,CAACuB,KAAK,EAAEtB,SAAS,CAAC;EACzD,MAAMY,UAAU,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;IACrCiC,aAAa;IACb1C,KAAK;IACL4C,SAAS,EAAEC,SAAS;IACpB5C;EACF,CAAC,CAAC;EACF,MAAMC,OAAO,GAAGJ,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACQ,QAAQ,EAAEpB,QAAQ,CAAC;IAC1C8D,EAAE,EAAEF,SAAS;IACbF,SAAS,EAAErD,IAAI,CAACoD,aAAa;IAC7B;IACA;IACA,aAAa,EAAExC,OAAO,CAACE,IAAI,EAAEuC,SAAS,CAAC;IACvC5C,UAAU,EAAEA,UAAU;IACtB,aAAa,EAAE,IAAI;IACnB0C,GAAG,EAAEA;EACP,CAAC,EAAEK,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGZ,IAAI,CAACa,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;EACET,aAAa,EAAErD,SAAS,CAAC+D,MAAM;EAC/B;AACF;AACA;EACEC,QAAQ,EAAEhE,SAAS,CAACiE,IAAI;EACxB;AACF;AACA;EACEpD,OAAO,EAAEb,SAAS,CAACkE,MAAM;EACzB;AACF;AACA;EACEZ,SAAS,EAAEtD,SAAS,CAAC+D,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;EACEpD,KAAK,EAAEX,SAAS,CAAC,sCAAsCmE,SAAS,CAAC,CAACnE,SAAS,CAACoE,KAAK,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAEpE,SAAS,CAAC+D,MAAM,CAAC,CAAC;EACvM;AACF;AACA;AACA;EACER,SAAS,EAAEvD,SAAS,CAACqE,WAAW;EAChC;AACF;AACA;AACA;EACEzD,QAAQ,EAAEZ,SAAS,CAAC,sCAAsCmE,SAAS,CAAC,CAACnE,SAAS,CAACoE,KAAK,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAEpE,SAAS,CAAC+D,MAAM,CAAC,CAAC;EACjJ;AACF;AACA;EACEO,EAAE,EAAEtE,SAAS,CAACmE,SAAS,CAAC,CAACnE,SAAS,CAACuE,OAAO,CAACvE,SAAS,CAACmE,SAAS,CAAC,CAACnE,SAAS,CAACwE,IAAI,EAAExE,SAAS,CAACkE,MAAM,EAAElE,SAAS,CAACyE,IAAI,CAAC,CAAC,CAAC,EAAEzE,SAAS,CAACwE,IAAI,EAAExE,SAAS,CAACkE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACVjB,IAAI,CAACyB,OAAO,GAAG,MAAM;AACrB,eAAezB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}