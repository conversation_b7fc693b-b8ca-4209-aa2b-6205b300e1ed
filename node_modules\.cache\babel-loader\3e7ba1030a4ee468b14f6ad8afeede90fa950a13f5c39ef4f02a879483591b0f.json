{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSwitchBaseUtilityClass(slot) {\n  return generateUtilityClass('PrivateSwitchBase', slot);\n}\nconst switchBaseClasses = generateUtilityClasses('PrivateSwitchBase', ['root', 'checked', 'disabled', 'input', 'edgeStart', 'edgeEnd']);\nexport default switchBaseClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getSwitchBaseUtilityClass", "slot", "switchBaseClasses"], "sources": ["D:/Safety Tracking Web App/node_modules/@mui/material/internal/switchBaseClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSwitchBaseUtilityClass(slot) {\n  return generateUtilityClass('PrivateSwitchBase', slot);\n}\nconst switchBaseClasses = generateUtilityClasses('PrivateSwitchBase', ['root', 'checked', 'disabled', 'input', 'edgeStart', 'edgeEnd']);\nexport default switchBaseClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAOF,oBAAoB,CAAC,mBAAmB,EAAEE,IAAI,CAAC;AACxD;AACA,MAAMC,iBAAiB,GAAGJ,sBAAsB,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;AACvI,eAAeI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}