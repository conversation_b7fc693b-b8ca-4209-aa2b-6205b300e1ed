{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useControlled as useControlled, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useEventCallback as useEventCallback, unstable_useForkRef as useForkRef, unstable_useIsFocusVisible as useIsFocusVisible, visuallyHidden, clamp } from '@mui/utils';\nimport extractEventHandlers from '@mui/utils/extractEventHandlers';\nimport areArraysEqual from '../utils/areArraysEqual';\nconst INTENTIONAL_DRAG_COUNT_THRESHOLD = 2;\nfunction asc(a, b) {\n  return a - b;\n}\nfunction findClosest(values, currentValue) {\n  var _values$reduce;\n  const {\n    index: closestIndex\n  } = (_values$reduce = values.reduce((acc, value, index) => {\n    const distance = Math.abs(currentValue - value);\n    if (acc === null || distance < acc.distance || distance === acc.distance) {\n      return {\n        distance,\n        index\n      };\n    }\n    return acc;\n  }, null)) != null ? _values$reduce : {};\n  return closestIndex;\n}\nfunction trackFinger(event, touchId) {\n  // The event is TouchEvent\n  if (touchId.current !== undefined && event.changedTouches) {\n    const touchEvent = event;\n    for (let i = 0; i < touchEvent.changedTouches.length; i += 1) {\n      const touch = touchEvent.changedTouches[i];\n      if (touch.identifier === touchId.current) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n\n  // The event is MouseEvent\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nexport function valueToPercent(value, min, max) {\n  return (value - min) * 100 / (max - min);\n}\nfunction percentToValue(percent, min, max) {\n  return (max - min) * percent + min;\n}\nfunction getDecimalPrecision(num) {\n  // This handles the case when num is very small (0.00000001), js will turn this into 1e-8.\n  // When num is bigger than 1 or less than -1 it won't get converted to this notation so it's fine.\n  if (Math.abs(num) < 1) {\n    const parts = num.toExponential().split('e-');\n    const matissaDecimalPart = parts[0].split('.')[1];\n    return (matissaDecimalPart ? matissaDecimalPart.length : 0) + parseInt(parts[1], 10);\n  }\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToStep(value, step, min) {\n  const nearest = Math.round((value - min) / step) * step + min;\n  return Number(nearest.toFixed(getDecimalPrecision(step)));\n}\nfunction setValueIndex({\n  values,\n  newValue,\n  index\n}) {\n  const output = values.slice();\n  output[index] = newValue;\n  return output.sort(asc);\n}\nfunction focusThumb({\n  sliderRef,\n  activeIndex,\n  setActive\n}) {\n  var _sliderRef$current, _doc$activeElement;\n  const doc = ownerDocument(sliderRef.current);\n  if (!((_sliderRef$current = sliderRef.current) != null && _sliderRef$current.contains(doc.activeElement)) || Number(doc == null || (_doc$activeElement = doc.activeElement) == null ? void 0 : _doc$activeElement.getAttribute('data-index')) !== activeIndex) {\n    var _sliderRef$current2;\n    (_sliderRef$current2 = sliderRef.current) == null || _sliderRef$current2.querySelector(`[type=\"range\"][data-index=\"${activeIndex}\"]`).focus();\n  }\n  if (setActive) {\n    setActive(activeIndex);\n  }\n}\nfunction areValuesEqual(newValue, oldValue) {\n  if (typeof newValue === 'number' && typeof oldValue === 'number') {\n    return newValue === oldValue;\n  }\n  if (typeof newValue === 'object' && typeof oldValue === 'object') {\n    return areArraysEqual(newValue, oldValue);\n  }\n  return false;\n}\nconst axisProps = {\n  horizontal: {\n    offset: percent => ({\n      left: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  'horizontal-reverse': {\n    offset: percent => ({\n      right: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  vertical: {\n    offset: percent => ({\n      bottom: `${percent}%`\n    }),\n    leap: percent => ({\n      height: `${percent}%`\n    })\n  }\n};\nexport const Identity = x => x;\n\n// TODO: remove support for Safari < 13.\n// https://caniuse.com/#search=touch-action\n//\n// Safari, on iOS, supports touch action since v13.\n// Over 80% of the iOS phones are compatible\n// in August 2020.\n// Utilizing the CSS.supports method to check if touch-action is supported.\n// Since CSS.supports is supported on all but Edge@12 and IE and touch-action\n// is supported on both Edge@12 and IE if CSS.supports is not available that means that\n// touch-action will be supported\nlet cachedSupportsTouchActionNone;\nfunction doesSupportTouchActionNone() {\n  if (cachedSupportsTouchActionNone === undefined) {\n    if (typeof CSS !== 'undefined' && typeof CSS.supports === 'function') {\n      cachedSupportsTouchActionNone = CSS.supports('touch-action', 'none');\n    } else {\n      cachedSupportsTouchActionNone = true;\n    }\n  }\n  return cachedSupportsTouchActionNone;\n}\n/**\n *\n * Demos:\n *\n * - [Slider](https://mui.com/base-ui/react-slider/#hook)\n *\n * API:\n *\n * - [useSlider API](https://mui.com/base-ui/react-slider/hooks-api/#use-slider)\n */\nexport function useSlider(parameters) {\n  const {\n    'aria-labelledby': ariaLabelledby,\n    defaultValue,\n    disabled = false,\n    disableSwap = false,\n    isRtl = false,\n    marks: marksProp = false,\n    max = 100,\n    min = 0,\n    name,\n    onChange,\n    onChangeCommitted,\n    orientation = 'horizontal',\n    rootRef: ref,\n    scale = Identity,\n    step = 1,\n    shiftStep = 10,\n    tabIndex,\n    value: valueProp\n  } = parameters;\n  const touchId = React.useRef(undefined);\n  // We can't use the :active browser pseudo-classes.\n  // - The active state isn't triggered when clicking on the rail.\n  // - The active state isn't transferred when inversing a range slider.\n  const [active, setActive] = React.useState(-1);\n  const [open, setOpen] = React.useState(-1);\n  const [dragging, setDragging] = React.useState(false);\n  const moveCount = React.useRef(0);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue != null ? defaultValue : min,\n    name: 'Slider'\n  });\n  const handleChange = onChange && ((event, value, thumbIndex) => {\n    // Redefine target to allow name and value to be read.\n    // This allows seamless integration with the most popular form libraries.\n    // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n    // Clone the event to not override `target` of the original event.\n    const nativeEvent = event.nativeEvent || event;\n    // @ts-ignore The nativeEvent is function, not object\n    const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n    Object.defineProperty(clonedEvent, 'target', {\n      writable: true,\n      value: {\n        value,\n        name\n      }\n    });\n    onChange(clonedEvent, value, thumbIndex);\n  });\n  const range = Array.isArray(valueDerived);\n  let values = range ? valueDerived.slice().sort(asc) : [valueDerived];\n  values = values.map(value => value == null ? min : clamp(value, min, max));\n  const marks = marksProp === true && step !== null ? [...Array(Math.floor((max - min) / step) + 1)].map((_, index) => ({\n    value: min + step * index\n  })) : marksProp || [];\n  const marksValues = marks.map(mark => mark.value);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusedThumbIndex, setFocusedThumbIndex] = React.useState(-1);\n  const sliderRef = React.useRef(null);\n  const handleFocusRef = useForkRef(focusVisibleRef, sliderRef);\n  const handleRef = useForkRef(ref, handleFocusRef);\n  const createHandleHiddenInputFocus = otherHandlers => event => {\n    var _otherHandlers$onFocu;\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusedThumbIndex(index);\n    }\n    setOpen(index);\n    otherHandlers == null || (_otherHandlers$onFocu = otherHandlers.onFocus) == null || _otherHandlers$onFocu.call(otherHandlers, event);\n  };\n  const createHandleHiddenInputBlur = otherHandlers => event => {\n    var _otherHandlers$onBlur;\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusedThumbIndex(-1);\n    }\n    setOpen(-1);\n    otherHandlers == null || (_otherHandlers$onBlur = otherHandlers.onBlur) == null || _otherHandlers$onBlur.call(otherHandlers, event);\n  };\n  const changeValue = (event, valueInput) => {\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    const value = values[index];\n    const marksIndex = marksValues.indexOf(value);\n    let newValue = valueInput;\n    if (marks && step == null) {\n      const maxMarksValue = marksValues[marksValues.length - 1];\n      if (newValue > maxMarksValue) {\n        newValue = maxMarksValue;\n      } else if (newValue < marksValues[0]) {\n        newValue = marksValues[0];\n      } else {\n        newValue = newValue < value ? marksValues[marksIndex - 1] : marksValues[marksIndex + 1];\n      }\n    }\n    newValue = clamp(newValue, min, max);\n    if (range) {\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[index - 1] || -Infinity, values[index + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index\n      });\n      let activeIndex = index;\n\n      // Potentially swap the index if needed.\n      if (!disableSwap) {\n        activeIndex = newValue.indexOf(previousValue);\n      }\n      focusThumb({\n        sliderRef,\n        activeIndex\n      });\n    }\n    setValueState(newValue);\n    setFocusedThumbIndex(index);\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(event, newValue, index);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(event, newValue);\n    }\n  };\n  const createHandleHiddenInputKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    // The Shift + Up/Down keyboard shortcuts for moving the slider makes sense to be supported\n    // only if the step is defined. If the step is null, this means tha the marks are used for specifying the valid values.\n    if (step !== null) {\n      const index = Number(event.currentTarget.getAttribute('data-index'));\n      const value = values[index];\n      let newValue = null;\n      if ((event.key === 'ArrowLeft' || event.key === 'ArrowDown') && event.shiftKey || event.key === 'PageDown') {\n        newValue = Math.max(value - shiftStep, min);\n      } else if ((event.key === 'ArrowRight' || event.key === 'ArrowUp') && event.shiftKey || event.key === 'PageUp') {\n        newValue = Math.min(value + shiftStep, max);\n      }\n      if (newValue !== null) {\n        changeValue(event, newValue);\n        event.preventDefault();\n      }\n    }\n    otherHandlers == null || (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null || _otherHandlers$onKeyD.call(otherHandlers, event);\n  };\n  useEnhancedEffect(() => {\n    if (disabled && sliderRef.current.contains(document.activeElement)) {\n      var _document$activeEleme;\n      // This is necessary because Firefox and Safari will keep focus\n      // on a disabled element:\n      // https://codesandbox.io/p/sandbox/mui-pr-22247-forked-h151h?file=/src/App.js\n      // @ts-ignore\n      (_document$activeEleme = document.activeElement) == null || _document$activeEleme.blur();\n    }\n  }, [disabled]);\n  if (disabled && active !== -1) {\n    setActive(-1);\n  }\n  if (disabled && focusedThumbIndex !== -1) {\n    setFocusedThumbIndex(-1);\n  }\n  const createHandleHiddenInputChange = otherHandlers => event => {\n    var _otherHandlers$onChan;\n    (_otherHandlers$onChan = otherHandlers.onChange) == null || _otherHandlers$onChan.call(otherHandlers, event);\n    // @ts-ignore\n    changeValue(event, event.target.valueAsNumber);\n  };\n  const previousIndex = React.useRef(undefined);\n  let axis = orientation;\n  if (isRtl && orientation === 'horizontal') {\n    axis += '-reverse';\n  }\n  const getFingerNewValue = ({\n    finger,\n    move = false\n  }) => {\n    const {\n      current: slider\n    } = sliderRef;\n    const {\n      width,\n      height,\n      bottom,\n      left\n    } = slider.getBoundingClientRect();\n    let percent;\n    if (axis.indexOf('vertical') === 0) {\n      percent = (bottom - finger.y) / height;\n    } else {\n      percent = (finger.x - left) / width;\n    }\n    if (axis.indexOf('-reverse') !== -1) {\n      percent = 1 - percent;\n    }\n    let newValue;\n    newValue = percentToValue(percent, min, max);\n    if (step) {\n      newValue = roundValueToStep(newValue, step, min);\n    } else {\n      const closestIndex = findClosest(marksValues, newValue);\n      newValue = marksValues[closestIndex];\n    }\n    newValue = clamp(newValue, min, max);\n    let activeIndex = 0;\n    if (range) {\n      if (!move) {\n        activeIndex = findClosest(values, newValue);\n      } else {\n        activeIndex = previousIndex.current;\n      }\n\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[activeIndex - 1] || -Infinity, values[activeIndex + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index: activeIndex\n      });\n\n      // Potentially swap the index if needed.\n      if (!(disableSwap && move)) {\n        activeIndex = newValue.indexOf(previousValue);\n        previousIndex.current = activeIndex;\n      }\n    }\n    return {\n      newValue,\n      activeIndex\n    };\n  };\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    if (!finger) {\n      return;\n    }\n    moveCount.current += 1;\n\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    // @ts-ignore buttons doesn't not exists on touch event\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    const {\n      newValue,\n      activeIndex\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    focusThumb({\n      sliderRef,\n      activeIndex,\n      setActive\n    });\n    setValueState(newValue);\n    if (!dragging && moveCount.current > INTENTIONAL_DRAG_COUNT_THRESHOLD) {\n      setDragging(true);\n    }\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(nativeEvent, newValue, activeIndex);\n    }\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    setDragging(false);\n    if (!finger) {\n      return;\n    }\n    const {\n      newValue\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    setActive(-1);\n    if (nativeEvent.type === 'touchend') {\n      setOpen(-1);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(nativeEvent, newValue);\n    }\n    touchId.current = undefined;\n\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n  });\n  const handleTouchStart = useEventCallback(nativeEvent => {\n    if (disabled) {\n      return;\n    }\n    // If touch-action: none; is not supported we need to prevent the scroll manually.\n    if (!doesSupportTouchActionNone()) {\n      nativeEvent.preventDefault();\n    }\n    const touch = nativeEvent.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const finger = trackFinger(nativeEvent, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(nativeEvent, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('touchmove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('touchend', handleTouchEnd, {\n      passive: true\n    });\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(sliderRef.current);\n    doc.removeEventListener('mousemove', handleTouchMove);\n    doc.removeEventListener('mouseup', handleTouchEnd);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n  }, [handleTouchEnd, handleTouchMove]);\n  React.useEffect(() => {\n    const {\n      current: slider\n    } = sliderRef;\n    slider.addEventListener('touchstart', handleTouchStart, {\n      passive: doesSupportTouchActionNone()\n    });\n    return () => {\n      slider.removeEventListener('touchstart', handleTouchStart);\n      stopListening();\n    };\n  }, [stopListening, handleTouchStart]);\n  React.useEffect(() => {\n    if (disabled) {\n      stopListening();\n    }\n  }, [disabled, stopListening]);\n  const createHandleMouseDown = otherHandlers => event => {\n    var _otherHandlers$onMous;\n    (_otherHandlers$onMous = otherHandlers.onMouseDown) == null || _otherHandlers$onMous.call(otherHandlers, event);\n    if (disabled) {\n      return;\n    }\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    const finger = trackFinger(event, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(event, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('mousemove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('mouseup', handleTouchEnd);\n  };\n  const trackOffset = valueToPercent(range ? values[0] : min, min, max);\n  const trackLeap = valueToPercent(values[values.length - 1], min, max) - trackOffset;\n  const getRootProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseDown: createHandleMouseDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({}, externalProps, {\n      ref: handleRef\n    }, mergedEventHandlers);\n  };\n  const createHandleMouseOver = otherHandlers => event => {\n    var _otherHandlers$onMous2;\n    (_otherHandlers$onMous2 = otherHandlers.onMouseOver) == null || _otherHandlers$onMous2.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    setOpen(index);\n  };\n  const createHandleMouseLeave = otherHandlers => event => {\n    var _otherHandlers$onMous3;\n    (_otherHandlers$onMous3 = otherHandlers.onMouseLeave) == null || _otherHandlers$onMous3.call(otherHandlers, event);\n    setOpen(-1);\n  };\n  const getThumbProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseOver: createHandleMouseOver(externalHandlers || {}),\n      onMouseLeave: createHandleMouseLeave(externalHandlers || {})\n    };\n    return _extends({}, externalProps, externalHandlers, ownEventHandlers);\n  };\n  const getThumbStyle = index => {\n    return {\n      // So the non active thumb doesn't show its label on hover.\n      pointerEvents: active !== -1 && active !== index ? 'none' : undefined\n    };\n  };\n  const getHiddenInputProps = (externalProps = {}) => {\n    var _parameters$step;\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onChange: createHandleHiddenInputChange(externalHandlers || {}),\n      onFocus: createHandleHiddenInputFocus(externalHandlers || {}),\n      onBlur: createHandleHiddenInputBlur(externalHandlers || {}),\n      onKeyDown: createHandleHiddenInputKeyDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({\n      tabIndex,\n      'aria-labelledby': ariaLabelledby,\n      'aria-orientation': orientation,\n      'aria-valuemax': scale(max),\n      'aria-valuemin': scale(min),\n      name,\n      type: 'range',\n      min: parameters.min,\n      max: parameters.max,\n      step: parameters.step === null && parameters.marks ? 'any' : (_parameters$step = parameters.step) != null ? _parameters$step : undefined,\n      disabled\n    }, externalProps, mergedEventHandlers, {\n      style: _extends({}, visuallyHidden, {\n        direction: isRtl ? 'rtl' : 'ltr',\n        // So that VoiceOver's focus indicator matches the thumb's dimensions\n        width: '100%',\n        height: '100%'\n      })\n    });\n  };\n  return {\n    active,\n    axis: axis,\n    axisProps,\n    dragging,\n    focusedThumbIndex,\n    getHiddenInputProps,\n    getRootProps,\n    getThumbProps,\n    marks: marks,\n    open,\n    range,\n    rootRef: handleRef,\n    trackLeap,\n    trackOffset,\n    values,\n    getThumbStyle\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_ownerDocument", "ownerDocument", "unstable_useControlled", "useControlled", "unstable_useEnhancedEffect", "useEnhancedEffect", "unstable_useEventCallback", "useEventCallback", "unstable_useForkRef", "useForkRef", "unstable_useIsFocusVisible", "useIsFocusVisible", "visuallyHidden", "clamp", "extractEventHandlers", "areArraysEqual", "INTENTIONAL_DRAG_COUNT_THRESHOLD", "asc", "a", "b", "findClosest", "values", "currentValue", "_values$reduce", "index", "closestIndex", "reduce", "acc", "value", "distance", "Math", "abs", "trackFinger", "event", "touchId", "current", "undefined", "changedTouches", "touchEvent", "i", "length", "touch", "identifier", "x", "clientX", "y", "clientY", "valueToPercent", "min", "max", "percentToValue", "percent", "getDecimalPrecision", "num", "parts", "toExponential", "split", "mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parseInt", "decimalPart", "toString", "roundValueToStep", "step", "nearest", "round", "Number", "toFixed", "setValueIndex", "newValue", "output", "slice", "sort", "focusThumb", "sliderRef", "activeIndex", "setActive", "_sliderRef$current", "_doc$activeElement", "doc", "contains", "activeElement", "getAttribute", "_sliderRef$current2", "querySelector", "focus", "areValuesEqual", "oldValue", "axisProps", "horizontal", "offset", "left", "leap", "width", "right", "vertical", "bottom", "height", "Identity", "cachedSupportsTouchActionNone", "doesSupportTouchActionNone", "CSS", "supports", "useSlider", "parameters", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultValue", "disabled", "disableSwap", "isRtl", "marks", "marksProp", "name", "onChange", "onChangeCommitted", "orientation", "rootRef", "ref", "scale", "shiftStep", "tabIndex", "valueProp", "useRef", "active", "useState", "open", "<PERSON><PERSON><PERSON>", "dragging", "setDragging", "moveCount", "valueDerived", "setValueState", "controlled", "default", "handleChange", "thumbIndex", "nativeEvent", "clonedEvent", "constructor", "type", "Object", "defineProperty", "writable", "range", "Array", "isArray", "map", "floor", "_", "marksV<PERSON>ues", "mark", "isFocusVisibleRef", "onBlur", "handleBlurVisible", "onFocus", "handleFocusVisible", "focusVisibleRef", "focusedThumbIndex", "setFocusedThumbIndex", "handleFocusRef", "handleRef", "createHandleHiddenInputFocus", "otherHandlers", "_otherHandlers$onFocu", "currentTarget", "call", "createHandleHiddenInputBlur", "_otherHandlers$onBlur", "changeValue", "valueInput", "marksIndex", "indexOf", "maxMarksValue", "Infinity", "previousValue", "createHandleHiddenInputKeyDown", "_otherHandlers$onKeyD", "key", "shift<PERSON>ey", "preventDefault", "onKeyDown", "document", "_document$activeEleme", "blur", "createHandleHiddenInputChange", "_otherHandlers$onChan", "target", "valueAsNumber", "previousIndex", "axis", "getFingerNewValue", "finger", "move", "slider", "getBoundingClientRect", "handleTouchMove", "buttons", "handleTouchEnd", "stopListening", "handleTouchStart", "addEventListener", "passive", "useCallback", "removeEventListener", "useEffect", "createHandleMouseDown", "_otherHandlers$onMous", "onMouseDown", "defaultPrevented", "button", "trackOffset", "trackLeap", "getRootProps", "externalProps", "externalHandlers", "ownEventHandlers", "mergedEventHandlers", "createHandleMouseOver", "_otherHandlers$onMous2", "onMouseOver", "createHandleMouseLeave", "_otherHandlers$onMous3", "onMouseLeave", "getThumbProps", "getThumbStyle", "pointerEvents", "getHiddenInputProps", "_parameters$step", "style", "direction"], "sources": ["D:/Safety Tracking Web App/node_modules/@mui/material/Slider/useSlider.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useControlled as useControlled, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useEventCallback as useEventCallback, unstable_useForkRef as useForkRef, unstable_useIsFocusVisible as useIsFocusVisible, visuallyHidden, clamp } from '@mui/utils';\nimport extractEventHandlers from '@mui/utils/extractEventHandlers';\nimport areArraysEqual from '../utils/areArraysEqual';\nconst INTENTIONAL_DRAG_COUNT_THRESHOLD = 2;\nfunction asc(a, b) {\n  return a - b;\n}\nfunction findClosest(values, currentValue) {\n  var _values$reduce;\n  const {\n    index: closestIndex\n  } = (_values$reduce = values.reduce((acc, value, index) => {\n    const distance = Math.abs(currentValue - value);\n    if (acc === null || distance < acc.distance || distance === acc.distance) {\n      return {\n        distance,\n        index\n      };\n    }\n    return acc;\n  }, null)) != null ? _values$reduce : {};\n  return closestIndex;\n}\nfunction trackFinger(event, touchId) {\n  // The event is TouchEvent\n  if (touchId.current !== undefined && event.changedTouches) {\n    const touchEvent = event;\n    for (let i = 0; i < touchEvent.changedTouches.length; i += 1) {\n      const touch = touchEvent.changedTouches[i];\n      if (touch.identifier === touchId.current) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n\n  // The event is MouseEvent\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nexport function valueToPercent(value, min, max) {\n  return (value - min) * 100 / (max - min);\n}\nfunction percentToValue(percent, min, max) {\n  return (max - min) * percent + min;\n}\nfunction getDecimalPrecision(num) {\n  // This handles the case when num is very small (0.00000001), js will turn this into 1e-8.\n  // When num is bigger than 1 or less than -1 it won't get converted to this notation so it's fine.\n  if (Math.abs(num) < 1) {\n    const parts = num.toExponential().split('e-');\n    const matissaDecimalPart = parts[0].split('.')[1];\n    return (matissaDecimalPart ? matissaDecimalPart.length : 0) + parseInt(parts[1], 10);\n  }\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToStep(value, step, min) {\n  const nearest = Math.round((value - min) / step) * step + min;\n  return Number(nearest.toFixed(getDecimalPrecision(step)));\n}\nfunction setValueIndex({\n  values,\n  newValue,\n  index\n}) {\n  const output = values.slice();\n  output[index] = newValue;\n  return output.sort(asc);\n}\nfunction focusThumb({\n  sliderRef,\n  activeIndex,\n  setActive\n}) {\n  var _sliderRef$current, _doc$activeElement;\n  const doc = ownerDocument(sliderRef.current);\n  if (!((_sliderRef$current = sliderRef.current) != null && _sliderRef$current.contains(doc.activeElement)) || Number(doc == null || (_doc$activeElement = doc.activeElement) == null ? void 0 : _doc$activeElement.getAttribute('data-index')) !== activeIndex) {\n    var _sliderRef$current2;\n    (_sliderRef$current2 = sliderRef.current) == null || _sliderRef$current2.querySelector(`[type=\"range\"][data-index=\"${activeIndex}\"]`).focus();\n  }\n  if (setActive) {\n    setActive(activeIndex);\n  }\n}\nfunction areValuesEqual(newValue, oldValue) {\n  if (typeof newValue === 'number' && typeof oldValue === 'number') {\n    return newValue === oldValue;\n  }\n  if (typeof newValue === 'object' && typeof oldValue === 'object') {\n    return areArraysEqual(newValue, oldValue);\n  }\n  return false;\n}\nconst axisProps = {\n  horizontal: {\n    offset: percent => ({\n      left: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  'horizontal-reverse': {\n    offset: percent => ({\n      right: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  vertical: {\n    offset: percent => ({\n      bottom: `${percent}%`\n    }),\n    leap: percent => ({\n      height: `${percent}%`\n    })\n  }\n};\nexport const Identity = x => x;\n\n// TODO: remove support for Safari < 13.\n// https://caniuse.com/#search=touch-action\n//\n// Safari, on iOS, supports touch action since v13.\n// Over 80% of the iOS phones are compatible\n// in August 2020.\n// Utilizing the CSS.supports method to check if touch-action is supported.\n// Since CSS.supports is supported on all but Edge@12 and IE and touch-action\n// is supported on both Edge@12 and IE if CSS.supports is not available that means that\n// touch-action will be supported\nlet cachedSupportsTouchActionNone;\nfunction doesSupportTouchActionNone() {\n  if (cachedSupportsTouchActionNone === undefined) {\n    if (typeof CSS !== 'undefined' && typeof CSS.supports === 'function') {\n      cachedSupportsTouchActionNone = CSS.supports('touch-action', 'none');\n    } else {\n      cachedSupportsTouchActionNone = true;\n    }\n  }\n  return cachedSupportsTouchActionNone;\n}\n/**\n *\n * Demos:\n *\n * - [Slider](https://mui.com/base-ui/react-slider/#hook)\n *\n * API:\n *\n * - [useSlider API](https://mui.com/base-ui/react-slider/hooks-api/#use-slider)\n */\nexport function useSlider(parameters) {\n  const {\n    'aria-labelledby': ariaLabelledby,\n    defaultValue,\n    disabled = false,\n    disableSwap = false,\n    isRtl = false,\n    marks: marksProp = false,\n    max = 100,\n    min = 0,\n    name,\n    onChange,\n    onChangeCommitted,\n    orientation = 'horizontal',\n    rootRef: ref,\n    scale = Identity,\n    step = 1,\n    shiftStep = 10,\n    tabIndex,\n    value: valueProp\n  } = parameters;\n  const touchId = React.useRef(undefined);\n  // We can't use the :active browser pseudo-classes.\n  // - The active state isn't triggered when clicking on the rail.\n  // - The active state isn't transferred when inversing a range slider.\n  const [active, setActive] = React.useState(-1);\n  const [open, setOpen] = React.useState(-1);\n  const [dragging, setDragging] = React.useState(false);\n  const moveCount = React.useRef(0);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue != null ? defaultValue : min,\n    name: 'Slider'\n  });\n  const handleChange = onChange && ((event, value, thumbIndex) => {\n    // Redefine target to allow name and value to be read.\n    // This allows seamless integration with the most popular form libraries.\n    // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n    // Clone the event to not override `target` of the original event.\n    const nativeEvent = event.nativeEvent || event;\n    // @ts-ignore The nativeEvent is function, not object\n    const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n    Object.defineProperty(clonedEvent, 'target', {\n      writable: true,\n      value: {\n        value,\n        name\n      }\n    });\n    onChange(clonedEvent, value, thumbIndex);\n  });\n  const range = Array.isArray(valueDerived);\n  let values = range ? valueDerived.slice().sort(asc) : [valueDerived];\n  values = values.map(value => value == null ? min : clamp(value, min, max));\n  const marks = marksProp === true && step !== null ? [...Array(Math.floor((max - min) / step) + 1)].map((_, index) => ({\n    value: min + step * index\n  })) : marksProp || [];\n  const marksValues = marks.map(mark => mark.value);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusedThumbIndex, setFocusedThumbIndex] = React.useState(-1);\n  const sliderRef = React.useRef(null);\n  const handleFocusRef = useForkRef(focusVisibleRef, sliderRef);\n  const handleRef = useForkRef(ref, handleFocusRef);\n  const createHandleHiddenInputFocus = otherHandlers => event => {\n    var _otherHandlers$onFocu;\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusedThumbIndex(index);\n    }\n    setOpen(index);\n    otherHandlers == null || (_otherHandlers$onFocu = otherHandlers.onFocus) == null || _otherHandlers$onFocu.call(otherHandlers, event);\n  };\n  const createHandleHiddenInputBlur = otherHandlers => event => {\n    var _otherHandlers$onBlur;\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusedThumbIndex(-1);\n    }\n    setOpen(-1);\n    otherHandlers == null || (_otherHandlers$onBlur = otherHandlers.onBlur) == null || _otherHandlers$onBlur.call(otherHandlers, event);\n  };\n  const changeValue = (event, valueInput) => {\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    const value = values[index];\n    const marksIndex = marksValues.indexOf(value);\n    let newValue = valueInput;\n    if (marks && step == null) {\n      const maxMarksValue = marksValues[marksValues.length - 1];\n      if (newValue > maxMarksValue) {\n        newValue = maxMarksValue;\n      } else if (newValue < marksValues[0]) {\n        newValue = marksValues[0];\n      } else {\n        newValue = newValue < value ? marksValues[marksIndex - 1] : marksValues[marksIndex + 1];\n      }\n    }\n    newValue = clamp(newValue, min, max);\n    if (range) {\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[index - 1] || -Infinity, values[index + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index\n      });\n      let activeIndex = index;\n\n      // Potentially swap the index if needed.\n      if (!disableSwap) {\n        activeIndex = newValue.indexOf(previousValue);\n      }\n      focusThumb({\n        sliderRef,\n        activeIndex\n      });\n    }\n    setValueState(newValue);\n    setFocusedThumbIndex(index);\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(event, newValue, index);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(event, newValue);\n    }\n  };\n  const createHandleHiddenInputKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    // The Shift + Up/Down keyboard shortcuts for moving the slider makes sense to be supported\n    // only if the step is defined. If the step is null, this means tha the marks are used for specifying the valid values.\n    if (step !== null) {\n      const index = Number(event.currentTarget.getAttribute('data-index'));\n      const value = values[index];\n      let newValue = null;\n      if ((event.key === 'ArrowLeft' || event.key === 'ArrowDown') && event.shiftKey || event.key === 'PageDown') {\n        newValue = Math.max(value - shiftStep, min);\n      } else if ((event.key === 'ArrowRight' || event.key === 'ArrowUp') && event.shiftKey || event.key === 'PageUp') {\n        newValue = Math.min(value + shiftStep, max);\n      }\n      if (newValue !== null) {\n        changeValue(event, newValue);\n        event.preventDefault();\n      }\n    }\n    otherHandlers == null || (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null || _otherHandlers$onKeyD.call(otherHandlers, event);\n  };\n  useEnhancedEffect(() => {\n    if (disabled && sliderRef.current.contains(document.activeElement)) {\n      var _document$activeEleme;\n      // This is necessary because Firefox and Safari will keep focus\n      // on a disabled element:\n      // https://codesandbox.io/p/sandbox/mui-pr-22247-forked-h151h?file=/src/App.js\n      // @ts-ignore\n      (_document$activeEleme = document.activeElement) == null || _document$activeEleme.blur();\n    }\n  }, [disabled]);\n  if (disabled && active !== -1) {\n    setActive(-1);\n  }\n  if (disabled && focusedThumbIndex !== -1) {\n    setFocusedThumbIndex(-1);\n  }\n  const createHandleHiddenInputChange = otherHandlers => event => {\n    var _otherHandlers$onChan;\n    (_otherHandlers$onChan = otherHandlers.onChange) == null || _otherHandlers$onChan.call(otherHandlers, event);\n    // @ts-ignore\n    changeValue(event, event.target.valueAsNumber);\n  };\n  const previousIndex = React.useRef(undefined);\n  let axis = orientation;\n  if (isRtl && orientation === 'horizontal') {\n    axis += '-reverse';\n  }\n  const getFingerNewValue = ({\n    finger,\n    move = false\n  }) => {\n    const {\n      current: slider\n    } = sliderRef;\n    const {\n      width,\n      height,\n      bottom,\n      left\n    } = slider.getBoundingClientRect();\n    let percent;\n    if (axis.indexOf('vertical') === 0) {\n      percent = (bottom - finger.y) / height;\n    } else {\n      percent = (finger.x - left) / width;\n    }\n    if (axis.indexOf('-reverse') !== -1) {\n      percent = 1 - percent;\n    }\n    let newValue;\n    newValue = percentToValue(percent, min, max);\n    if (step) {\n      newValue = roundValueToStep(newValue, step, min);\n    } else {\n      const closestIndex = findClosest(marksValues, newValue);\n      newValue = marksValues[closestIndex];\n    }\n    newValue = clamp(newValue, min, max);\n    let activeIndex = 0;\n    if (range) {\n      if (!move) {\n        activeIndex = findClosest(values, newValue);\n      } else {\n        activeIndex = previousIndex.current;\n      }\n\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[activeIndex - 1] || -Infinity, values[activeIndex + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index: activeIndex\n      });\n\n      // Potentially swap the index if needed.\n      if (!(disableSwap && move)) {\n        activeIndex = newValue.indexOf(previousValue);\n        previousIndex.current = activeIndex;\n      }\n    }\n    return {\n      newValue,\n      activeIndex\n    };\n  };\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    if (!finger) {\n      return;\n    }\n    moveCount.current += 1;\n\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    // @ts-ignore buttons doesn't not exists on touch event\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    const {\n      newValue,\n      activeIndex\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    focusThumb({\n      sliderRef,\n      activeIndex,\n      setActive\n    });\n    setValueState(newValue);\n    if (!dragging && moveCount.current > INTENTIONAL_DRAG_COUNT_THRESHOLD) {\n      setDragging(true);\n    }\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(nativeEvent, newValue, activeIndex);\n    }\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    setDragging(false);\n    if (!finger) {\n      return;\n    }\n    const {\n      newValue\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    setActive(-1);\n    if (nativeEvent.type === 'touchend') {\n      setOpen(-1);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(nativeEvent, newValue);\n    }\n    touchId.current = undefined;\n\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n  });\n  const handleTouchStart = useEventCallback(nativeEvent => {\n    if (disabled) {\n      return;\n    }\n    // If touch-action: none; is not supported we need to prevent the scroll manually.\n    if (!doesSupportTouchActionNone()) {\n      nativeEvent.preventDefault();\n    }\n    const touch = nativeEvent.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const finger = trackFinger(nativeEvent, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(nativeEvent, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('touchmove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('touchend', handleTouchEnd, {\n      passive: true\n    });\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(sliderRef.current);\n    doc.removeEventListener('mousemove', handleTouchMove);\n    doc.removeEventListener('mouseup', handleTouchEnd);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n  }, [handleTouchEnd, handleTouchMove]);\n  React.useEffect(() => {\n    const {\n      current: slider\n    } = sliderRef;\n    slider.addEventListener('touchstart', handleTouchStart, {\n      passive: doesSupportTouchActionNone()\n    });\n    return () => {\n      slider.removeEventListener('touchstart', handleTouchStart);\n      stopListening();\n    };\n  }, [stopListening, handleTouchStart]);\n  React.useEffect(() => {\n    if (disabled) {\n      stopListening();\n    }\n  }, [disabled, stopListening]);\n  const createHandleMouseDown = otherHandlers => event => {\n    var _otherHandlers$onMous;\n    (_otherHandlers$onMous = otherHandlers.onMouseDown) == null || _otherHandlers$onMous.call(otherHandlers, event);\n    if (disabled) {\n      return;\n    }\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    const finger = trackFinger(event, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(event, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('mousemove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('mouseup', handleTouchEnd);\n  };\n  const trackOffset = valueToPercent(range ? values[0] : min, min, max);\n  const trackLeap = valueToPercent(values[values.length - 1], min, max) - trackOffset;\n  const getRootProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseDown: createHandleMouseDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({}, externalProps, {\n      ref: handleRef\n    }, mergedEventHandlers);\n  };\n  const createHandleMouseOver = otherHandlers => event => {\n    var _otherHandlers$onMous2;\n    (_otherHandlers$onMous2 = otherHandlers.onMouseOver) == null || _otherHandlers$onMous2.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    setOpen(index);\n  };\n  const createHandleMouseLeave = otherHandlers => event => {\n    var _otherHandlers$onMous3;\n    (_otherHandlers$onMous3 = otherHandlers.onMouseLeave) == null || _otherHandlers$onMous3.call(otherHandlers, event);\n    setOpen(-1);\n  };\n  const getThumbProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseOver: createHandleMouseOver(externalHandlers || {}),\n      onMouseLeave: createHandleMouseLeave(externalHandlers || {})\n    };\n    return _extends({}, externalProps, externalHandlers, ownEventHandlers);\n  };\n  const getThumbStyle = index => {\n    return {\n      // So the non active thumb doesn't show its label on hover.\n      pointerEvents: active !== -1 && active !== index ? 'none' : undefined\n    };\n  };\n  const getHiddenInputProps = (externalProps = {}) => {\n    var _parameters$step;\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onChange: createHandleHiddenInputChange(externalHandlers || {}),\n      onFocus: createHandleHiddenInputFocus(externalHandlers || {}),\n      onBlur: createHandleHiddenInputBlur(externalHandlers || {}),\n      onKeyDown: createHandleHiddenInputKeyDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, externalHandlers, ownEventHandlers);\n    return _extends({\n      tabIndex,\n      'aria-labelledby': ariaLabelledby,\n      'aria-orientation': orientation,\n      'aria-valuemax': scale(max),\n      'aria-valuemin': scale(min),\n      name,\n      type: 'range',\n      min: parameters.min,\n      max: parameters.max,\n      step: parameters.step === null && parameters.marks ? 'any' : (_parameters$step = parameters.step) != null ? _parameters$step : undefined,\n      disabled\n    }, externalProps, mergedEventHandlers, {\n      style: _extends({}, visuallyHidden, {\n        direction: isRtl ? 'rtl' : 'ltr',\n        // So that VoiceOver's focus indicator matches the thumb's dimensions\n        width: '100%',\n        height: '100%'\n      })\n    });\n  };\n  return {\n    active,\n    axis: axis,\n    axisProps,\n    dragging,\n    focusedThumbIndex,\n    getHiddenInputProps,\n    getRootProps,\n    getThumbProps,\n    marks: marks,\n    open,\n    range,\n    rootRef: handleRef,\n    trackLeap,\n    trackOffset,\n    values,\n    getThumbStyle\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,IAAIC,aAAa,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,yBAAyB,IAAIC,gBAAgB,EAAEC,mBAAmB,IAAIC,UAAU,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,cAAc,EAAEC,KAAK,QAAQ,YAAY;AACxT,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,cAAc,MAAM,yBAAyB;AACpD,MAAMC,gCAAgC,GAAG,CAAC;AAC1C,SAASC,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjB,OAAOD,CAAC,GAAGC,CAAC;AACd;AACA,SAASC,WAAWA,CAACC,MAAM,EAAEC,YAAY,EAAE;EACzC,IAAIC,cAAc;EAClB,MAAM;IACJC,KAAK,EAAEC;EACT,CAAC,GAAG,CAACF,cAAc,GAAGF,MAAM,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,EAAEJ,KAAK,KAAK;IACzD,MAAMK,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACT,YAAY,GAAGM,KAAK,CAAC;IAC/C,IAAID,GAAG,KAAK,IAAI,IAAIE,QAAQ,GAAGF,GAAG,CAACE,QAAQ,IAAIA,QAAQ,KAAKF,GAAG,CAACE,QAAQ,EAAE;MACxE,OAAO;QACLA,QAAQ;QACRL;MACF,CAAC;IACH;IACA,OAAOG,GAAG;EACZ,CAAC,EAAE,IAAI,CAAC,KAAK,IAAI,GAAGJ,cAAc,GAAG,CAAC,CAAC;EACvC,OAAOE,YAAY;AACrB;AACA,SAASO,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACnC;EACA,IAAIA,OAAO,CAACC,OAAO,KAAKC,SAAS,IAAIH,KAAK,CAACI,cAAc,EAAE;IACzD,MAAMC,UAAU,GAAGL,KAAK;IACxB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACD,cAAc,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAC5D,MAAME,KAAK,GAAGH,UAAU,CAACD,cAAc,CAACE,CAAC,CAAC;MAC1C,IAAIE,KAAK,CAACC,UAAU,KAAKR,OAAO,CAACC,OAAO,EAAE;QACxC,OAAO;UACLQ,CAAC,EAAEF,KAAK,CAACG,OAAO;UAChBC,CAAC,EAAEJ,KAAK,CAACK;QACX,CAAC;MACH;IACF;IACA,OAAO,KAAK;EACd;;EAEA;EACA,OAAO;IACLH,CAAC,EAAEV,KAAK,CAACW,OAAO;IAChBC,CAAC,EAAEZ,KAAK,CAACa;EACX,CAAC;AACH;AACA,OAAO,SAASC,cAAcA,CAACnB,KAAK,EAAEoB,GAAG,EAAEC,GAAG,EAAE;EAC9C,OAAO,CAACrB,KAAK,GAAGoB,GAAG,IAAI,GAAG,IAAIC,GAAG,GAAGD,GAAG,CAAC;AAC1C;AACA,SAASE,cAAcA,CAACC,OAAO,EAAEH,GAAG,EAAEC,GAAG,EAAE;EACzC,OAAO,CAACA,GAAG,GAAGD,GAAG,IAAIG,OAAO,GAAGH,GAAG;AACpC;AACA,SAASI,mBAAmBA,CAACC,GAAG,EAAE;EAChC;EACA;EACA,IAAIvB,IAAI,CAACC,GAAG,CAACsB,GAAG,CAAC,GAAG,CAAC,EAAE;IACrB,MAAMC,KAAK,GAAGD,GAAG,CAACE,aAAa,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7C,MAAMC,kBAAkB,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjD,OAAO,CAACC,kBAAkB,GAAGA,kBAAkB,CAACjB,MAAM,GAAG,CAAC,IAAIkB,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACtF;EACA,MAAMK,WAAW,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChD,OAAOG,WAAW,GAAGA,WAAW,CAACnB,MAAM,GAAG,CAAC;AAC7C;AACA,SAASqB,gBAAgBA,CAACjC,KAAK,EAAEkC,IAAI,EAAEd,GAAG,EAAE;EAC1C,MAAMe,OAAO,GAAGjC,IAAI,CAACkC,KAAK,CAAC,CAACpC,KAAK,GAAGoB,GAAG,IAAIc,IAAI,CAAC,GAAGA,IAAI,GAAGd,GAAG;EAC7D,OAAOiB,MAAM,CAACF,OAAO,CAACG,OAAO,CAACd,mBAAmB,CAACU,IAAI,CAAC,CAAC,CAAC;AAC3D;AACA,SAASK,aAAaA,CAAC;EACrB9C,MAAM;EACN+C,QAAQ;EACR5C;AACF,CAAC,EAAE;EACD,MAAM6C,MAAM,GAAGhD,MAAM,CAACiD,KAAK,CAAC,CAAC;EAC7BD,MAAM,CAAC7C,KAAK,CAAC,GAAG4C,QAAQ;EACxB,OAAOC,MAAM,CAACE,IAAI,CAACtD,GAAG,CAAC;AACzB;AACA,SAASuD,UAAUA,CAAC;EAClBC,SAAS;EACTC,WAAW;EACXC;AACF,CAAC,EAAE;EACD,IAAIC,kBAAkB,EAAEC,kBAAkB;EAC1C,MAAMC,GAAG,GAAG7E,aAAa,CAACwE,SAAS,CAACtC,OAAO,CAAC;EAC5C,IAAI,EAAE,CAACyC,kBAAkB,GAAGH,SAAS,CAACtC,OAAO,KAAK,IAAI,IAAIyC,kBAAkB,CAACG,QAAQ,CAACD,GAAG,CAACE,aAAa,CAAC,CAAC,IAAIf,MAAM,CAACa,GAAG,IAAI,IAAI,IAAI,CAACD,kBAAkB,GAAGC,GAAG,CAACE,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,kBAAkB,CAACI,YAAY,CAAC,YAAY,CAAC,CAAC,KAAKP,WAAW,EAAE;IAC7P,IAAIQ,mBAAmB;IACvB,CAACA,mBAAmB,GAAGT,SAAS,CAACtC,OAAO,KAAK,IAAI,IAAI+C,mBAAmB,CAACC,aAAa,CAAC,8BAA8BT,WAAW,IAAI,CAAC,CAACU,KAAK,CAAC,CAAC;EAC/I;EACA,IAAIT,SAAS,EAAE;IACbA,SAAS,CAACD,WAAW,CAAC;EACxB;AACF;AACA,SAASW,cAAcA,CAACjB,QAAQ,EAAEkB,QAAQ,EAAE;EAC1C,IAAI,OAAOlB,QAAQ,KAAK,QAAQ,IAAI,OAAOkB,QAAQ,KAAK,QAAQ,EAAE;IAChE,OAAOlB,QAAQ,KAAKkB,QAAQ;EAC9B;EACA,IAAI,OAAOlB,QAAQ,KAAK,QAAQ,IAAI,OAAOkB,QAAQ,KAAK,QAAQ,EAAE;IAChE,OAAOvE,cAAc,CAACqD,QAAQ,EAAEkB,QAAQ,CAAC;EAC3C;EACA,OAAO,KAAK;AACd;AACA,MAAMC,SAAS,GAAG;EAChBC,UAAU,EAAE;IACVC,MAAM,EAAEtC,OAAO,KAAK;MAClBuC,IAAI,EAAE,GAAGvC,OAAO;IAClB,CAAC,CAAC;IACFwC,IAAI,EAAExC,OAAO,KAAK;MAChByC,KAAK,EAAE,GAAGzC,OAAO;IACnB,CAAC;EACH,CAAC;EACD,oBAAoB,EAAE;IACpBsC,MAAM,EAAEtC,OAAO,KAAK;MAClB0C,KAAK,EAAE,GAAG1C,OAAO;IACnB,CAAC,CAAC;IACFwC,IAAI,EAAExC,OAAO,KAAK;MAChByC,KAAK,EAAE,GAAGzC,OAAO;IACnB,CAAC;EACH,CAAC;EACD2C,QAAQ,EAAE;IACRL,MAAM,EAAEtC,OAAO,KAAK;MAClB4C,MAAM,EAAE,GAAG5C,OAAO;IACpB,CAAC,CAAC;IACFwC,IAAI,EAAExC,OAAO,KAAK;MAChB6C,MAAM,EAAE,GAAG7C,OAAO;IACpB,CAAC;EACH;AACF,CAAC;AACD,OAAO,MAAM8C,QAAQ,GAAGtD,CAAC,IAAIA,CAAC;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIuD,6BAA6B;AACjC,SAASC,0BAA0BA,CAAA,EAAG;EACpC,IAAID,6BAA6B,KAAK9D,SAAS,EAAE;IAC/C,IAAI,OAAOgE,GAAG,KAAK,WAAW,IAAI,OAAOA,GAAG,CAACC,QAAQ,KAAK,UAAU,EAAE;MACpEH,6BAA6B,GAAGE,GAAG,CAACC,QAAQ,CAAC,cAAc,EAAE,MAAM,CAAC;IACtE,CAAC,MAAM;MACLH,6BAA6B,GAAG,IAAI;IACtC;EACF;EACA,OAAOA,6BAA6B;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,SAASA,CAACC,UAAU,EAAE;EACpC,MAAM;IACJ,iBAAiB,EAAEC,cAAc;IACjCC,YAAY;IACZC,QAAQ,GAAG,KAAK;IAChBC,WAAW,GAAG,KAAK;IACnBC,KAAK,GAAG,KAAK;IACbC,KAAK,EAAEC,SAAS,GAAG,KAAK;IACxB7D,GAAG,GAAG,GAAG;IACTD,GAAG,GAAG,CAAC;IACP+D,IAAI;IACJC,QAAQ;IACRC,iBAAiB;IACjBC,WAAW,GAAG,YAAY;IAC1BC,OAAO,EAAEC,GAAG;IACZC,KAAK,GAAGpB,QAAQ;IAChBnC,IAAI,GAAG,CAAC;IACRwD,SAAS,GAAG,EAAE;IACdC,QAAQ;IACR3F,KAAK,EAAE4F;EACT,CAAC,GAAGjB,UAAU;EACd,MAAMrE,OAAO,GAAGnC,KAAK,CAAC0H,MAAM,CAACrF,SAAS,CAAC;EACvC;EACA;EACA;EACA,MAAM,CAACsF,MAAM,EAAE/C,SAAS,CAAC,GAAG5E,KAAK,CAAC4H,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG9H,KAAK,CAAC4H,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGhI,KAAK,CAAC4H,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMK,SAAS,GAAGjI,KAAK,CAAC0H,MAAM,CAAC,CAAC,CAAC;EACjC,MAAM,CAACQ,YAAY,EAAEC,aAAa,CAAC,GAAG/H,aAAa,CAAC;IAClDgI,UAAU,EAAEX,SAAS;IACrBY,OAAO,EAAE3B,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGzD,GAAG;IAClD+D,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMsB,YAAY,GAAGrB,QAAQ,KAAK,CAAC/E,KAAK,EAAEL,KAAK,EAAE0G,UAAU,KAAK;IAC9D;IACA;IACA;IACA;IACA,MAAMC,WAAW,GAAGtG,KAAK,CAACsG,WAAW,IAAItG,KAAK;IAC9C;IACA,MAAMuG,WAAW,GAAG,IAAID,WAAW,CAACE,WAAW,CAACF,WAAW,CAACG,IAAI,EAAEH,WAAW,CAAC;IAC9EI,MAAM,CAACC,cAAc,CAACJ,WAAW,EAAE,QAAQ,EAAE;MAC3CK,QAAQ,EAAE,IAAI;MACdjH,KAAK,EAAE;QACLA,KAAK;QACLmF;MACF;IACF,CAAC,CAAC;IACFC,QAAQ,CAACwB,WAAW,EAAE5G,KAAK,EAAE0G,UAAU,CAAC;EAC1C,CAAC,CAAC;EACF,MAAMQ,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACf,YAAY,CAAC;EACzC,IAAI5G,MAAM,GAAGyH,KAAK,GAAGb,YAAY,CAAC3D,KAAK,CAAC,CAAC,CAACC,IAAI,CAACtD,GAAG,CAAC,GAAG,CAACgH,YAAY,CAAC;EACpE5G,MAAM,GAAGA,MAAM,CAAC4H,GAAG,CAACrH,KAAK,IAAIA,KAAK,IAAI,IAAI,GAAGoB,GAAG,GAAGnC,KAAK,CAACe,KAAK,EAAEoB,GAAG,EAAEC,GAAG,CAAC,CAAC;EAC1E,MAAM4D,KAAK,GAAGC,SAAS,KAAK,IAAI,IAAIhD,IAAI,KAAK,IAAI,GAAG,CAAC,GAAGiF,KAAK,CAACjH,IAAI,CAACoH,KAAK,CAAC,CAACjG,GAAG,GAAGD,GAAG,IAAIc,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAACmF,GAAG,CAAC,CAACE,CAAC,EAAE3H,KAAK,MAAM;IACpHI,KAAK,EAAEoB,GAAG,GAAGc,IAAI,GAAGtC;EACtB,CAAC,CAAC,CAAC,GAAGsF,SAAS,IAAI,EAAE;EACrB,MAAMsC,WAAW,GAAGvC,KAAK,CAACoC,GAAG,CAACI,IAAI,IAAIA,IAAI,CAACzH,KAAK,CAAC;EACjD,MAAM;IACJ0H,iBAAiB;IACjBC,MAAM,EAAEC,iBAAiB;IACzBC,OAAO,EAAEC,kBAAkB;IAC3BtC,GAAG,EAAEuC;EACP,CAAC,GAAGhJ,iBAAiB,CAAC,CAAC;EACvB,MAAM,CAACiJ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9J,KAAK,CAAC4H,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpE,MAAMlD,SAAS,GAAG1E,KAAK,CAAC0H,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMqC,cAAc,GAAGrJ,UAAU,CAACkJ,eAAe,EAAElF,SAAS,CAAC;EAC7D,MAAMsF,SAAS,GAAGtJ,UAAU,CAAC2G,GAAG,EAAE0C,cAAc,CAAC;EACjD,MAAME,4BAA4B,GAAGC,aAAa,IAAIhI,KAAK,IAAI;IAC7D,IAAIiI,qBAAqB;IACzB,MAAM1I,KAAK,GAAGyC,MAAM,CAAChC,KAAK,CAACkI,aAAa,CAAClF,YAAY,CAAC,YAAY,CAAC,CAAC;IACpEyE,kBAAkB,CAACzH,KAAK,CAAC;IACzB,IAAIqH,iBAAiB,CAACnH,OAAO,KAAK,IAAI,EAAE;MACtC0H,oBAAoB,CAACrI,KAAK,CAAC;IAC7B;IACAqG,OAAO,CAACrG,KAAK,CAAC;IACdyI,aAAa,IAAI,IAAI,IAAI,CAACC,qBAAqB,GAAGD,aAAa,CAACR,OAAO,KAAK,IAAI,IAAIS,qBAAqB,CAACE,IAAI,CAACH,aAAa,EAAEhI,KAAK,CAAC;EACtI,CAAC;EACD,MAAMoI,2BAA2B,GAAGJ,aAAa,IAAIhI,KAAK,IAAI;IAC5D,IAAIqI,qBAAqB;IACzBd,iBAAiB,CAACvH,KAAK,CAAC;IACxB,IAAIqH,iBAAiB,CAACnH,OAAO,KAAK,KAAK,EAAE;MACvC0H,oBAAoB,CAAC,CAAC,CAAC,CAAC;IAC1B;IACAhC,OAAO,CAAC,CAAC,CAAC,CAAC;IACXoC,aAAa,IAAI,IAAI,IAAI,CAACK,qBAAqB,GAAGL,aAAa,CAACV,MAAM,KAAK,IAAI,IAAIe,qBAAqB,CAACF,IAAI,CAACH,aAAa,EAAEhI,KAAK,CAAC;EACrI,CAAC;EACD,MAAMsI,WAAW,GAAGA,CAACtI,KAAK,EAAEuI,UAAU,KAAK;IACzC,MAAMhJ,KAAK,GAAGyC,MAAM,CAAChC,KAAK,CAACkI,aAAa,CAAClF,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE,MAAMrD,KAAK,GAAGP,MAAM,CAACG,KAAK,CAAC;IAC3B,MAAMiJ,UAAU,GAAGrB,WAAW,CAACsB,OAAO,CAAC9I,KAAK,CAAC;IAC7C,IAAIwC,QAAQ,GAAGoG,UAAU;IACzB,IAAI3D,KAAK,IAAI/C,IAAI,IAAI,IAAI,EAAE;MACzB,MAAM6G,aAAa,GAAGvB,WAAW,CAACA,WAAW,CAAC5G,MAAM,GAAG,CAAC,CAAC;MACzD,IAAI4B,QAAQ,GAAGuG,aAAa,EAAE;QAC5BvG,QAAQ,GAAGuG,aAAa;MAC1B,CAAC,MAAM,IAAIvG,QAAQ,GAAGgF,WAAW,CAAC,CAAC,CAAC,EAAE;QACpChF,QAAQ,GAAGgF,WAAW,CAAC,CAAC,CAAC;MAC3B,CAAC,MAAM;QACLhF,QAAQ,GAAGA,QAAQ,GAAGxC,KAAK,GAAGwH,WAAW,CAACqB,UAAU,GAAG,CAAC,CAAC,GAAGrB,WAAW,CAACqB,UAAU,GAAG,CAAC,CAAC;MACzF;IACF;IACArG,QAAQ,GAAGvD,KAAK,CAACuD,QAAQ,EAAEpB,GAAG,EAAEC,GAAG,CAAC;IACpC,IAAI6F,KAAK,EAAE;MACT;MACA,IAAInC,WAAW,EAAE;QACfvC,QAAQ,GAAGvD,KAAK,CAACuD,QAAQ,EAAE/C,MAAM,CAACG,KAAK,GAAG,CAAC,CAAC,IAAI,CAACoJ,QAAQ,EAAEvJ,MAAM,CAACG,KAAK,GAAG,CAAC,CAAC,IAAIoJ,QAAQ,CAAC;MAC3F;MACA,MAAMC,aAAa,GAAGzG,QAAQ;MAC9BA,QAAQ,GAAGD,aAAa,CAAC;QACvB9C,MAAM;QACN+C,QAAQ;QACR5C;MACF,CAAC,CAAC;MACF,IAAIkD,WAAW,GAAGlD,KAAK;;MAEvB;MACA,IAAI,CAACmF,WAAW,EAAE;QAChBjC,WAAW,GAAGN,QAAQ,CAACsG,OAAO,CAACG,aAAa,CAAC;MAC/C;MACArG,UAAU,CAAC;QACTC,SAAS;QACTC;MACF,CAAC,CAAC;IACJ;IACAwD,aAAa,CAAC9D,QAAQ,CAAC;IACvByF,oBAAoB,CAACrI,KAAK,CAAC;IAC3B,IAAI6G,YAAY,IAAI,CAAChD,cAAc,CAACjB,QAAQ,EAAE6D,YAAY,CAAC,EAAE;MAC3DI,YAAY,CAACpG,KAAK,EAAEmC,QAAQ,EAAE5C,KAAK,CAAC;IACtC;IACA,IAAIyF,iBAAiB,EAAE;MACrBA,iBAAiB,CAAChF,KAAK,EAAEmC,QAAQ,CAAC;IACpC;EACF,CAAC;EACD,MAAM0G,8BAA8B,GAAGb,aAAa,IAAIhI,KAAK,IAAI;IAC/D,IAAI8I,qBAAqB;IACzB;IACA;IACA,IAAIjH,IAAI,KAAK,IAAI,EAAE;MACjB,MAAMtC,KAAK,GAAGyC,MAAM,CAAChC,KAAK,CAACkI,aAAa,CAAClF,YAAY,CAAC,YAAY,CAAC,CAAC;MACpE,MAAMrD,KAAK,GAAGP,MAAM,CAACG,KAAK,CAAC;MAC3B,IAAI4C,QAAQ,GAAG,IAAI;MACnB,IAAI,CAACnC,KAAK,CAAC+I,GAAG,KAAK,WAAW,IAAI/I,KAAK,CAAC+I,GAAG,KAAK,WAAW,KAAK/I,KAAK,CAACgJ,QAAQ,IAAIhJ,KAAK,CAAC+I,GAAG,KAAK,UAAU,EAAE;QAC1G5G,QAAQ,GAAGtC,IAAI,CAACmB,GAAG,CAACrB,KAAK,GAAG0F,SAAS,EAAEtE,GAAG,CAAC;MAC7C,CAAC,MAAM,IAAI,CAACf,KAAK,CAAC+I,GAAG,KAAK,YAAY,IAAI/I,KAAK,CAAC+I,GAAG,KAAK,SAAS,KAAK/I,KAAK,CAACgJ,QAAQ,IAAIhJ,KAAK,CAAC+I,GAAG,KAAK,QAAQ,EAAE;QAC9G5G,QAAQ,GAAGtC,IAAI,CAACkB,GAAG,CAACpB,KAAK,GAAG0F,SAAS,EAAErE,GAAG,CAAC;MAC7C;MACA,IAAImB,QAAQ,KAAK,IAAI,EAAE;QACrBmG,WAAW,CAACtI,KAAK,EAAEmC,QAAQ,CAAC;QAC5BnC,KAAK,CAACiJ,cAAc,CAAC,CAAC;MACxB;IACF;IACAjB,aAAa,IAAI,IAAI,IAAI,CAACc,qBAAqB,GAAGd,aAAa,CAACkB,SAAS,KAAK,IAAI,IAAIJ,qBAAqB,CAACX,IAAI,CAACH,aAAa,EAAEhI,KAAK,CAAC;EACxI,CAAC;EACD5B,iBAAiB,CAAC,MAAM;IACtB,IAAIqG,QAAQ,IAAIjC,SAAS,CAACtC,OAAO,CAAC4C,QAAQ,CAACqG,QAAQ,CAACpG,aAAa,CAAC,EAAE;MAClE,IAAIqG,qBAAqB;MACzB;MACA;MACA;MACA;MACA,CAACA,qBAAqB,GAAGD,QAAQ,CAACpG,aAAa,KAAK,IAAI,IAAIqG,qBAAqB,CAACC,IAAI,CAAC,CAAC;IAC1F;EACF,CAAC,EAAE,CAAC5E,QAAQ,CAAC,CAAC;EACd,IAAIA,QAAQ,IAAIgB,MAAM,KAAK,CAAC,CAAC,EAAE;IAC7B/C,SAAS,CAAC,CAAC,CAAC,CAAC;EACf;EACA,IAAI+B,QAAQ,IAAIkD,iBAAiB,KAAK,CAAC,CAAC,EAAE;IACxCC,oBAAoB,CAAC,CAAC,CAAC,CAAC;EAC1B;EACA,MAAM0B,6BAA6B,GAAGtB,aAAa,IAAIhI,KAAK,IAAI;IAC9D,IAAIuJ,qBAAqB;IACzB,CAACA,qBAAqB,GAAGvB,aAAa,CAACjD,QAAQ,KAAK,IAAI,IAAIwE,qBAAqB,CAACpB,IAAI,CAACH,aAAa,EAAEhI,KAAK,CAAC;IAC5G;IACAsI,WAAW,CAACtI,KAAK,EAAEA,KAAK,CAACwJ,MAAM,CAACC,aAAa,CAAC;EAChD,CAAC;EACD,MAAMC,aAAa,GAAG5L,KAAK,CAAC0H,MAAM,CAACrF,SAAS,CAAC;EAC7C,IAAIwJ,IAAI,GAAG1E,WAAW;EACtB,IAAIN,KAAK,IAAIM,WAAW,KAAK,YAAY,EAAE;IACzC0E,IAAI,IAAI,UAAU;EACpB;EACA,MAAMC,iBAAiB,GAAGA,CAAC;IACzBC,MAAM;IACNC,IAAI,GAAG;EACT,CAAC,KAAK;IACJ,MAAM;MACJ5J,OAAO,EAAE6J;IACX,CAAC,GAAGvH,SAAS;IACb,MAAM;MACJmB,KAAK;MACLI,MAAM;MACND,MAAM;MACNL;IACF,CAAC,GAAGsG,MAAM,CAACC,qBAAqB,CAAC,CAAC;IAClC,IAAI9I,OAAO;IACX,IAAIyI,IAAI,CAAClB,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;MAClCvH,OAAO,GAAG,CAAC4C,MAAM,GAAG+F,MAAM,CAACjJ,CAAC,IAAImD,MAAM;IACxC,CAAC,MAAM;MACL7C,OAAO,GAAG,CAAC2I,MAAM,CAACnJ,CAAC,GAAG+C,IAAI,IAAIE,KAAK;IACrC;IACA,IAAIgG,IAAI,CAAClB,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;MACnCvH,OAAO,GAAG,CAAC,GAAGA,OAAO;IACvB;IACA,IAAIiB,QAAQ;IACZA,QAAQ,GAAGlB,cAAc,CAACC,OAAO,EAAEH,GAAG,EAAEC,GAAG,CAAC;IAC5C,IAAIa,IAAI,EAAE;MACRM,QAAQ,GAAGP,gBAAgB,CAACO,QAAQ,EAAEN,IAAI,EAAEd,GAAG,CAAC;IAClD,CAAC,MAAM;MACL,MAAMvB,YAAY,GAAGL,WAAW,CAACgI,WAAW,EAAEhF,QAAQ,CAAC;MACvDA,QAAQ,GAAGgF,WAAW,CAAC3H,YAAY,CAAC;IACtC;IACA2C,QAAQ,GAAGvD,KAAK,CAACuD,QAAQ,EAAEpB,GAAG,EAAEC,GAAG,CAAC;IACpC,IAAIyB,WAAW,GAAG,CAAC;IACnB,IAAIoE,KAAK,EAAE;MACT,IAAI,CAACiD,IAAI,EAAE;QACTrH,WAAW,GAAGtD,WAAW,CAACC,MAAM,EAAE+C,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACLM,WAAW,GAAGiH,aAAa,CAACxJ,OAAO;MACrC;;MAEA;MACA,IAAIwE,WAAW,EAAE;QACfvC,QAAQ,GAAGvD,KAAK,CAACuD,QAAQ,EAAE/C,MAAM,CAACqD,WAAW,GAAG,CAAC,CAAC,IAAI,CAACkG,QAAQ,EAAEvJ,MAAM,CAACqD,WAAW,GAAG,CAAC,CAAC,IAAIkG,QAAQ,CAAC;MACvG;MACA,MAAMC,aAAa,GAAGzG,QAAQ;MAC9BA,QAAQ,GAAGD,aAAa,CAAC;QACvB9C,MAAM;QACN+C,QAAQ;QACR5C,KAAK,EAAEkD;MACT,CAAC,CAAC;;MAEF;MACA,IAAI,EAAEiC,WAAW,IAAIoF,IAAI,CAAC,EAAE;QAC1BrH,WAAW,GAAGN,QAAQ,CAACsG,OAAO,CAACG,aAAa,CAAC;QAC7Cc,aAAa,CAACxJ,OAAO,GAAGuC,WAAW;MACrC;IACF;IACA,OAAO;MACLN,QAAQ;MACRM;IACF,CAAC;EACH,CAAC;EACD,MAAMwH,eAAe,GAAG3L,gBAAgB,CAACgI,WAAW,IAAI;IACtD,MAAMuD,MAAM,GAAG9J,WAAW,CAACuG,WAAW,EAAErG,OAAO,CAAC;IAChD,IAAI,CAAC4J,MAAM,EAAE;MACX;IACF;IACA9D,SAAS,CAAC7F,OAAO,IAAI,CAAC;;IAEtB;IACA;IACA,IAAIoG,WAAW,CAACG,IAAI,KAAK,WAAW,IAAIH,WAAW,CAAC4D,OAAO,KAAK,CAAC,EAAE;MACjE;MACAC,cAAc,CAAC7D,WAAW,CAAC;MAC3B;IACF;IACA,MAAM;MACJnE,QAAQ;MACRM;IACF,CAAC,GAAGmH,iBAAiB,CAAC;MACpBC,MAAM;MACNC,IAAI,EAAE;IACR,CAAC,CAAC;IACFvH,UAAU,CAAC;MACTC,SAAS;MACTC,WAAW;MACXC;IACF,CAAC,CAAC;IACFuD,aAAa,CAAC9D,QAAQ,CAAC;IACvB,IAAI,CAAC0D,QAAQ,IAAIE,SAAS,CAAC7F,OAAO,GAAGnB,gCAAgC,EAAE;MACrE+G,WAAW,CAAC,IAAI,CAAC;IACnB;IACA,IAAIM,YAAY,IAAI,CAAChD,cAAc,CAACjB,QAAQ,EAAE6D,YAAY,CAAC,EAAE;MAC3DI,YAAY,CAACE,WAAW,EAAEnE,QAAQ,EAAEM,WAAW,CAAC;IAClD;EACF,CAAC,CAAC;EACF,MAAM0H,cAAc,GAAG7L,gBAAgB,CAACgI,WAAW,IAAI;IACrD,MAAMuD,MAAM,GAAG9J,WAAW,CAACuG,WAAW,EAAErG,OAAO,CAAC;IAChD6F,WAAW,CAAC,KAAK,CAAC;IAClB,IAAI,CAAC+D,MAAM,EAAE;MACX;IACF;IACA,MAAM;MACJ1H;IACF,CAAC,GAAGyH,iBAAiB,CAAC;MACpBC,MAAM;MACNC,IAAI,EAAE;IACR,CAAC,CAAC;IACFpH,SAAS,CAAC,CAAC,CAAC,CAAC;IACb,IAAI4D,WAAW,CAACG,IAAI,KAAK,UAAU,EAAE;MACnCb,OAAO,CAAC,CAAC,CAAC,CAAC;IACb;IACA,IAAIZ,iBAAiB,EAAE;MACrBA,iBAAiB,CAACsB,WAAW,EAAEnE,QAAQ,CAAC;IAC1C;IACAlC,OAAO,CAACC,OAAO,GAAGC,SAAS;;IAE3B;IACAiK,aAAa,CAAC,CAAC;EACjB,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAG/L,gBAAgB,CAACgI,WAAW,IAAI;IACvD,IAAI7B,QAAQ,EAAE;MACZ;IACF;IACA;IACA,IAAI,CAACP,0BAA0B,CAAC,CAAC,EAAE;MACjCoC,WAAW,CAAC2C,cAAc,CAAC,CAAC;IAC9B;IACA,MAAMzI,KAAK,GAAG8F,WAAW,CAAClG,cAAc,CAAC,CAAC,CAAC;IAC3C,IAAII,KAAK,IAAI,IAAI,EAAE;MACjB;MACAP,OAAO,CAACC,OAAO,GAAGM,KAAK,CAACC,UAAU;IACpC;IACA,MAAMoJ,MAAM,GAAG9J,WAAW,CAACuG,WAAW,EAAErG,OAAO,CAAC;IAChD,IAAI4J,MAAM,KAAK,KAAK,EAAE;MACpB,MAAM;QACJ1H,QAAQ;QACRM;MACF,CAAC,GAAGmH,iBAAiB,CAAC;QACpBC;MACF,CAAC,CAAC;MACFtH,UAAU,CAAC;QACTC,SAAS;QACTC,WAAW;QACXC;MACF,CAAC,CAAC;MACFuD,aAAa,CAAC9D,QAAQ,CAAC;MACvB,IAAIiE,YAAY,IAAI,CAAChD,cAAc,CAACjB,QAAQ,EAAE6D,YAAY,CAAC,EAAE;QAC3DI,YAAY,CAACE,WAAW,EAAEnE,QAAQ,EAAEM,WAAW,CAAC;MAClD;IACF;IACAsD,SAAS,CAAC7F,OAAO,GAAG,CAAC;IACrB,MAAM2C,GAAG,GAAG7E,aAAa,CAACwE,SAAS,CAACtC,OAAO,CAAC;IAC5C2C,GAAG,CAACyH,gBAAgB,CAAC,WAAW,EAAEL,eAAe,EAAE;MACjDM,OAAO,EAAE;IACX,CAAC,CAAC;IACF1H,GAAG,CAACyH,gBAAgB,CAAC,UAAU,EAAEH,cAAc,EAAE;MAC/CI,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMH,aAAa,GAAGtM,KAAK,CAAC0M,WAAW,CAAC,MAAM;IAC5C,MAAM3H,GAAG,GAAG7E,aAAa,CAACwE,SAAS,CAACtC,OAAO,CAAC;IAC5C2C,GAAG,CAAC4H,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;IACrDpH,GAAG,CAAC4H,mBAAmB,CAAC,SAAS,EAAEN,cAAc,CAAC;IAClDtH,GAAG,CAAC4H,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;IACrDpH,GAAG,CAAC4H,mBAAmB,CAAC,UAAU,EAAEN,cAAc,CAAC;EACrD,CAAC,EAAE,CAACA,cAAc,EAAEF,eAAe,CAAC,CAAC;EACrCnM,KAAK,CAAC4M,SAAS,CAAC,MAAM;IACpB,MAAM;MACJxK,OAAO,EAAE6J;IACX,CAAC,GAAGvH,SAAS;IACbuH,MAAM,CAACO,gBAAgB,CAAC,YAAY,EAAED,gBAAgB,EAAE;MACtDE,OAAO,EAAErG,0BAA0B,CAAC;IACtC,CAAC,CAAC;IACF,OAAO,MAAM;MACX6F,MAAM,CAACU,mBAAmB,CAAC,YAAY,EAAEJ,gBAAgB,CAAC;MAC1DD,aAAa,CAAC,CAAC;IACjB,CAAC;EACH,CAAC,EAAE,CAACA,aAAa,EAAEC,gBAAgB,CAAC,CAAC;EACrCvM,KAAK,CAAC4M,SAAS,CAAC,MAAM;IACpB,IAAIjG,QAAQ,EAAE;MACZ2F,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAAC3F,QAAQ,EAAE2F,aAAa,CAAC,CAAC;EAC7B,MAAMO,qBAAqB,GAAG3C,aAAa,IAAIhI,KAAK,IAAI;IACtD,IAAI4K,qBAAqB;IACzB,CAACA,qBAAqB,GAAG5C,aAAa,CAAC6C,WAAW,KAAK,IAAI,IAAID,qBAAqB,CAACzC,IAAI,CAACH,aAAa,EAAEhI,KAAK,CAAC;IAC/G,IAAIyE,QAAQ,EAAE;MACZ;IACF;IACA,IAAIzE,KAAK,CAAC8K,gBAAgB,EAAE;MAC1B;IACF;;IAEA;IACA,IAAI9K,KAAK,CAAC+K,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;;IAEA;IACA/K,KAAK,CAACiJ,cAAc,CAAC,CAAC;IACtB,MAAMY,MAAM,GAAG9J,WAAW,CAACC,KAAK,EAAEC,OAAO,CAAC;IAC1C,IAAI4J,MAAM,KAAK,KAAK,EAAE;MACpB,MAAM;QACJ1H,QAAQ;QACRM;MACF,CAAC,GAAGmH,iBAAiB,CAAC;QACpBC;MACF,CAAC,CAAC;MACFtH,UAAU,CAAC;QACTC,SAAS;QACTC,WAAW;QACXC;MACF,CAAC,CAAC;MACFuD,aAAa,CAAC9D,QAAQ,CAAC;MACvB,IAAIiE,YAAY,IAAI,CAAChD,cAAc,CAACjB,QAAQ,EAAE6D,YAAY,CAAC,EAAE;QAC3DI,YAAY,CAACpG,KAAK,EAAEmC,QAAQ,EAAEM,WAAW,CAAC;MAC5C;IACF;IACAsD,SAAS,CAAC7F,OAAO,GAAG,CAAC;IACrB,MAAM2C,GAAG,GAAG7E,aAAa,CAACwE,SAAS,CAACtC,OAAO,CAAC;IAC5C2C,GAAG,CAACyH,gBAAgB,CAAC,WAAW,EAAEL,eAAe,EAAE;MACjDM,OAAO,EAAE;IACX,CAAC,CAAC;IACF1H,GAAG,CAACyH,gBAAgB,CAAC,SAAS,EAAEH,cAAc,CAAC;EACjD,CAAC;EACD,MAAMa,WAAW,GAAGlK,cAAc,CAAC+F,KAAK,GAAGzH,MAAM,CAAC,CAAC,CAAC,GAAG2B,GAAG,EAAEA,GAAG,EAAEC,GAAG,CAAC;EACrE,MAAMiK,SAAS,GAAGnK,cAAc,CAAC1B,MAAM,CAACA,MAAM,CAACmB,MAAM,GAAG,CAAC,CAAC,EAAEQ,GAAG,EAAEC,GAAG,CAAC,GAAGgK,WAAW;EACnF,MAAME,YAAY,GAAGA,CAACC,aAAa,GAAG,CAAC,CAAC,KAAK;IAC3C,MAAMC,gBAAgB,GAAGvM,oBAAoB,CAACsM,aAAa,CAAC;IAC5D,MAAME,gBAAgB,GAAG;MACvBR,WAAW,EAAEF,qBAAqB,CAACS,gBAAgB,IAAI,CAAC,CAAC;IAC3D,CAAC;IACD,MAAME,mBAAmB,GAAGzN,QAAQ,CAAC,CAAC,CAAC,EAAEuN,gBAAgB,EAAEC,gBAAgB,CAAC;IAC5E,OAAOxN,QAAQ,CAAC,CAAC,CAAC,EAAEsN,aAAa,EAAE;MACjChG,GAAG,EAAE2C;IACP,CAAC,EAAEwD,mBAAmB,CAAC;EACzB,CAAC;EACD,MAAMC,qBAAqB,GAAGvD,aAAa,IAAIhI,KAAK,IAAI;IACtD,IAAIwL,sBAAsB;IAC1B,CAACA,sBAAsB,GAAGxD,aAAa,CAACyD,WAAW,KAAK,IAAI,IAAID,sBAAsB,CAACrD,IAAI,CAACH,aAAa,EAAEhI,KAAK,CAAC;IACjH,MAAMT,KAAK,GAAGyC,MAAM,CAAChC,KAAK,CAACkI,aAAa,CAAClF,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE4C,OAAO,CAACrG,KAAK,CAAC;EAChB,CAAC;EACD,MAAMmM,sBAAsB,GAAG1D,aAAa,IAAIhI,KAAK,IAAI;IACvD,IAAI2L,sBAAsB;IAC1B,CAACA,sBAAsB,GAAG3D,aAAa,CAAC4D,YAAY,KAAK,IAAI,IAAID,sBAAsB,CAACxD,IAAI,CAACH,aAAa,EAAEhI,KAAK,CAAC;IAClH4F,OAAO,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;EACD,MAAMiG,aAAa,GAAGA,CAACV,aAAa,GAAG,CAAC,CAAC,KAAK;IAC5C,MAAMC,gBAAgB,GAAGvM,oBAAoB,CAACsM,aAAa,CAAC;IAC5D,MAAME,gBAAgB,GAAG;MACvBI,WAAW,EAAEF,qBAAqB,CAACH,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC1DQ,YAAY,EAAEF,sBAAsB,CAACN,gBAAgB,IAAI,CAAC,CAAC;IAC7D,CAAC;IACD,OAAOvN,QAAQ,CAAC,CAAC,CAAC,EAAEsN,aAAa,EAAEC,gBAAgB,EAAEC,gBAAgB,CAAC;EACxE,CAAC;EACD,MAAMS,aAAa,GAAGvM,KAAK,IAAI;IAC7B,OAAO;MACL;MACAwM,aAAa,EAAEtG,MAAM,KAAK,CAAC,CAAC,IAAIA,MAAM,KAAKlG,KAAK,GAAG,MAAM,GAAGY;IAC9D,CAAC;EACH,CAAC;EACD,MAAM6L,mBAAmB,GAAGA,CAACb,aAAa,GAAG,CAAC,CAAC,KAAK;IAClD,IAAIc,gBAAgB;IACpB,MAAMb,gBAAgB,GAAGvM,oBAAoB,CAACsM,aAAa,CAAC;IAC5D,MAAME,gBAAgB,GAAG;MACvBtG,QAAQ,EAAEuE,6BAA6B,CAAC8B,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC/D5D,OAAO,EAAEO,4BAA4B,CAACqD,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC7D9D,MAAM,EAAEc,2BAA2B,CAACgD,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC3DlC,SAAS,EAAEL,8BAA8B,CAACuC,gBAAgB,IAAI,CAAC,CAAC;IAClE,CAAC;IACD,MAAME,mBAAmB,GAAGzN,QAAQ,CAAC,CAAC,CAAC,EAAEuN,gBAAgB,EAAEC,gBAAgB,CAAC;IAC5E,OAAOxN,QAAQ,CAAC;MACdyH,QAAQ;MACR,iBAAiB,EAAEf,cAAc;MACjC,kBAAkB,EAAEU,WAAW;MAC/B,eAAe,EAAEG,KAAK,CAACpE,GAAG,CAAC;MAC3B,eAAe,EAAEoE,KAAK,CAACrE,GAAG,CAAC;MAC3B+D,IAAI;MACJ2B,IAAI,EAAE,OAAO;MACb1F,GAAG,EAAEuD,UAAU,CAACvD,GAAG;MACnBC,GAAG,EAAEsD,UAAU,CAACtD,GAAG;MACnBa,IAAI,EAAEyC,UAAU,CAACzC,IAAI,KAAK,IAAI,IAAIyC,UAAU,CAACM,KAAK,GAAG,KAAK,GAAG,CAACqH,gBAAgB,GAAG3H,UAAU,CAACzC,IAAI,KAAK,IAAI,GAAGoK,gBAAgB,GAAG9L,SAAS;MACxIsE;IACF,CAAC,EAAE0G,aAAa,EAAEG,mBAAmB,EAAE;MACrCY,KAAK,EAAErO,QAAQ,CAAC,CAAC,CAAC,EAAEc,cAAc,EAAE;QAClCwN,SAAS,EAAExH,KAAK,GAAG,KAAK,GAAG,KAAK;QAChC;QACAhB,KAAK,EAAE,MAAM;QACbI,MAAM,EAAE;MACV,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EACD,OAAO;IACL0B,MAAM;IACNkE,IAAI,EAAEA,IAAI;IACVrG,SAAS;IACTuC,QAAQ;IACR8B,iBAAiB;IACjBqE,mBAAmB;IACnBd,YAAY;IACZW,aAAa;IACbjH,KAAK,EAAEA,KAAK;IACZe,IAAI;IACJkB,KAAK;IACL3B,OAAO,EAAE4C,SAAS;IAClBmD,SAAS;IACTD,WAAW;IACX5L,MAAM;IACN0M;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}